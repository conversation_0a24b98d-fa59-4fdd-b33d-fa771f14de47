# -*- coding: utf-8 -*-
"""
快速测试优化后的查询系统
使用现有CSV文件进行小批量测试
"""

import os
import sys
import pandas as pd
from high_performance_query import HighPerformanceQuerySystem

def find_csv_files():
    """查找当前目录的CSV文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    csv_files = []
    
    # 在父目录查找CSV文件
    for file in os.listdir(parent_dir):
        if file.endswith('.csv'):
            csv_files.append(os.path.join(parent_dir, file))
    
    return csv_files

def quick_test():
    """进行快速测试"""
    print("=== 海关查询系统快速测试 ===")
    
    # 查找CSV文件
    csv_files = find_csv_files()
    
    if not csv_files:
        print("未找到CSV文件，请确保有可用的批次号文件")
        return
    
    # 使用第一个CSV文件
    test_file = csv_files[0]
    print(f"使用测试文件: {os.path.basename(test_file)}")
    
    # 读取文件获取批次号数量
    try:
        df = pd.read_csv(test_file)
        total_count = len(df)
        print(f"文件中共有 {total_count} 个批次号")
        
        # 只测试前100个（如果有的话）
        test_count = min(100, total_count)
        print(f"将测试前 {test_count} 个批次号")
        
        # 创建测试用的小文件
        test_df = df.head(test_count)
        test_file_path = os.path.join(os.path.dirname(__file__), 'quick_test_data.csv')
        test_df.to_csv(test_file_path, index=False)
        
        print(f"测试数据已保存到: {test_file_path}")
        
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return
    
    # 创建查询系统
    print("\n--- 配置参数 ---")
    
    # 让用户选择并发数
    workers = input("请选择并发数 (建议15-25，默认20): ").strip()
    if not workers:
        workers = 20
    else:
        try:
            workers = int(workers)
        except:
            workers = 20
    
    print(f"使用并发数: {workers}")
    
    # 让用户选择查询类型
    print("查询类型:")
    print("1. 公路舱单查询 (road_transport)")
    print("2. 提运单信息查询 (road_query)")
    print("3. 双API查询 (dual) - 推荐")
    
    choice = input("请选择 (1/2/3，默认3): ").strip()
    
    if choice == '1':
        query_type = 'road_transport'
    elif choice == '2':
        query_type = 'road_query'
    else:
        query_type = 'dual'
    
    print(f"使用查询类型: {query_type}")
    
    # 开始测试
    print(f"\n{'='*50}")
    print("开始快速测试...")
    print(f"{'='*50}")
    
    query_system = HighPerformanceQuerySystem(max_workers=workers)
    
    output_dir = os.path.join(os.path.dirname(__file__), 'quick_test_results')
    
    if query_type == 'dual':
        results = query_system.run_dual_query(
            input_file=test_file_path,
            output_dir=output_dir
        )
    else:
        results = query_system.run_batch_query(
            input_file=test_file_path,
            query_type=query_type,
            output_dir=output_dir
        )
    
    print(f"\n快速测试完成!")
    print(f"结果已保存到: {output_dir}")
    print(f"\n如果测试效果满意，可以运行完整查询:")
    print(f"python high_performance_query.py {test_file} {query_type} --workers {workers}")

if __name__ == "__main__":
    quick_test()