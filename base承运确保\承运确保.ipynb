{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["欢迎使用ddddocr，本项目专注带动行业内卷，个人博客:wenanzhe.com\n", "训练数据支持来源于:http://146.56.204.113:19199/preview\n", "爬虫框架feapder可快速一键接入，快速开启爬虫之旅：https://github.com/Boris-code/feapder\n", "谷歌reCaptcha验证码 / hCaptcha验证码 / funCaptcha验证码商业级识别接口：https://yescaptcha.com/i/NSwk7i\n"]}], "source": ["from DrissionPage import Chromium, ChromiumOptions\n", "import pandas as pd\n", "import ddddocr\n", "import shutil\n", "import json\n", "import time\n", "# -*- coding: utf-8 -*-\n", "# @Software: PyCharm\n", "import requests\n", "import time\n", "import json\n", "from PIL import Image\n", "from io import BytesIO\n", "from collections import Counter\n", "import os\n", "import random\n", "\n", "\n", "\n", "\n", "# 先判断文件夹是否存在，如果不存在则创建它\n", "if not os.path.exists('数据落地'):\n", "    os.mkdir('数据落地')\n", "\n", "\n", "dddocr = ddddocr.DdddOcr()\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5100695287655 15066\n"]}], "source": ["# 读取excel数据\n", "# 读取当前路径的excel文件\n", "import os\n", "# 获取当前目录\n", "current_directory = os.getcwd()\n", "# 获取当前目录下所有的 CSV 文件\n", "csv_files = [f for f in os.listdir(current_directory) if f.endswith('.csv')]\n", "# 检查是否有 CSV 文件\n", "if csv_files:\n", "    # 取第一个 CSV 文件\n", "    first_csv_file = csv_files[0]\n", "    file_path = os.path.join(current_directory, first_csv_file)\n", "    df = pd.read_csv(file_path)\n", "    #print(df.info())\n", "\n", "# df = pd.read_csv('202412切割output_1.csv')\n", "    # 读取第一列的数据\n", "    data = df.iloc[:, 0].tolist()\n", "    print(data[0],len(data))\n", "\n", "#tab = Chromium().latest_tab\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["\n", "\n", "co = ChromiumOptions().auto_port()\n", "\n", "tab = Chromium(addr_or_opts=co).latest_tab\n", "\n", "\n", "#tab2 = Chromium(addr_or_opts=co).latest_tab\n", "\n", "#tab2.get('https://DrissionPage.cn')\n", "#tab.get('https://www.baidu.com')\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# 图片元素定位\n", "    # 定位元素\n", "def img_is_load_true():\n", "    element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1')\n", "\n", "    if element:\n", "        # 执行 JavaScript 检查图片是否加载完成\n", "        is_loaded = tab.run_js('return arguments[0].complete && (arguments[0].naturalWidth !== 0);', element)\n", "        if is_loaded:\n", "            print(\"图片已成功加载。\")\n", "            return True\n", "        else:\n", "            print(\"图片加载失败。\")\n", "            return False\n", "\n", "    else:\n", "        print(\"未找到指定元素。\")\n", "        return False\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def pass_yzm(i):\n", "    tab.get('http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx')\n", "\n", "    while not img_is_load_true():\n", "        tab.refresh()\n", "        time.sleep(1)\n", "\n", "    input_element = tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_txtManifestID\"]') # document.querySelector(\"#ID_codeName\")\n", "    input_element.input(i)\n", "\n", "        \n", "\n", "\n", "    # 定位图片元素\n", "    element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1')\n", "\n", "    element.get_screenshot()\n", "    bytes_str = element.get_screenshot(as_bytes='png')\n", "    result = dddocr.classification(bytes_str)\n", "    if len(result) == 4:\n", "        print('是四个验证码')\n", "        input_element = tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_txtCode\"]')\n", "        input_element.clear()\n", "\n", "        input_element.input(result)\n", "        #点击按钮\n", "        input_element = tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_btQuery\"]')\n", "        input_element.click()\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def ok_yzm():\n", "        # 使用 CSS 选择器定位元素\n", "            # 使用 XPath 定位元素，XPath 可以通过 CSS 选择器转换得到\n", "        element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1')\n", "        time.sleep(2)\n", "        if element:\n", "            print(\"成功定位到元素。\")\n", "            \n", "            # 可以在这里对元素进行其他操作，例如点击、获取属性等\n", "            # element.click()  # 点击元素\n", "            # src = element.attr('src')  # 获取元素的 src 属性\n", "            # print(f\"元素的 src 属性值为: {src}\")\n", "        else:\n", "            print(\"未找到指定元素。\")\n", "\n", "\n", "        element = tab.ele('@id=MRoadTransportQueryCtrl1_dgView')\n", "\n", "        # 执行 JavaScript 脚本提取指定 tr 元素下的 td 内容\n", "        js_code = \"\"\"\n", "        const tr = document.querySelector(\"#MRoadTransportQueryCtrl1_dgView > tbody > tr:nth-child(2)\");\n", "        if (tr) {\n", "            const tds = tr.querySelectorAll('td');\n", "            const tdContents = [];\n", "            tds.forEach(td => {\n", "                const a = td.querySelector('a');\n", "                if (a) {\n", "                    tdContents.push(a.textContent.trim());\n", "                } else {\n", "                    tdContents.push(td.textContent.trim());\n", "                }\n", "            });\n", "            return tdContents;\n", "        }\n", "        return null;\n", "        \"\"\"\n", "        td_contents = tab.run_js(js_code)\n", "\n", "        if td_contents:\n", "            print(\"提取到的 td 内容：\")\n", "            print(td_contents)\n", "            joined_list = '\\t'.join(map(str, td_contents))\n", "\n", "            with open(file_path, 'a', encoding='utf-8') as file:\n", "                # 遍历列表中的每个元素\n", "                file.write(joined_list)\n", "                # 写入换行符\n", "                file.write('\\n')\n", "\n", "            # for content in td_contents:\n", "            #     print(content)\n", "        else:\n", "            print(\"未找到指定的 tr 元素或未提取到 td 内容。\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["file_path = \"output.txt\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# # 验证码失败的按钮\n", "# tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_tdMessage\"]').text !='验证码填写不正确!'\n", "\n", "# # 验证码失败的按钮\n", "# tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_tdMessage\"]').text\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["图片加载失败。\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100695287655', '5340027680', '5345', '重车出口确报', '530175569117X_0000000000', '深圳市华信国际货运有限公司', '粤ZYC87港', '5300763008', '吴清辉', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100695287679', '5340063545', '5345', '重车出口确报', '530175569117X_0000000000', '深圳市华信国际货运有限公司', '粤ZYZ98港', '5300711495', '陈建成', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100700851639', '5340028386', '5345', '重车出口确报', '530175569117X_0000000000', '深圳市华信国际货运有限公司', '粤ZYB03港', '5300712177', '郑汉龙', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100717273780', '5340085719', '5345', '重车出口确报', '5300MA5FLE7FX_DXPESW0000210316', '深圳跨境贸易物流监管中心有限公司', '粤ZDW57港', '5300706774', '黄财生', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100711979211', '5340009423', '5345', '重车出口确报', '5300MA5FLE7FX_DXPESW0000210316', '深圳跨境贸易物流监管中心有限公司', '粤ZGH79港', '5300703454', '陈志坚', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100717238093', '5340026826', '5345', '重车出口确报', '5300MA5FLE7FX_DXPESW0000210316', '深圳跨境贸易物流监管中心有限公司', '粤ZZD67港', 'CG4916532', '陈敏材', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n", "图片已成功加载。\n", "是四个验证码\n", "成功定位到元素。\n", "提取到的 td 内容：\n", "['选择', '5100674098656', '5340063461', '5345', '重车出口确报', '5300MA5FLE7FX_DXPESW0000210316', '深圳跨境贸易物流监管中心有限公司', '粤ZSR41港', '5300706667', '黄建国', '已核销']\n", "图片已成功加载。\n", "是四个验证码\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[24], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m pass_yzm(i)\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (tab\u001b[38;5;241m.\u001b[39mele(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mx://*[@id=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMRoadTransportQueryCtrl1_tdMessage\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mtext \u001b[38;5;241m!=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m验证码填写不正确!\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m----> 5\u001b[0m     ok_yzm()\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n", "Cell \u001b[1;32mIn[21], line 5\u001b[0m, in \u001b[0;36mok_yzm\u001b[1;34m()\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mok_yzm\u001b[39m():\n\u001b[0;32m      2\u001b[0m         \u001b[38;5;66;03m# 使用 CSS 选择器定位元素\u001b[39;00m\n\u001b[0;32m      3\u001b[0m             \u001b[38;5;66;03m# 使用 XPath 定位元素，XPath 可以通过 CSS 选择器转换得到\u001b[39;00m\n\u001b[0;32m      4\u001b[0m         element \u001b[38;5;241m=\u001b[39m tab\u001b[38;5;241m.\u001b[39mele(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m@id=MRoadTransportQueryCtrl1_Image1\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 5\u001b[0m         time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m2\u001b[39m)\n\u001b[0;32m      6\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m element:\n\u001b[0;32m      7\u001b[0m             \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m成功定位到元素。\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "\n", "for i in data:\n", "    while True:\n", "        pass_yzm(i)\n", "        if (tab.ele('x://*[@id=\"MRoadTransportQueryCtrl1_tdMessage\"]').text !='验证码填写不正确!'):\n", "            ok_yzm()\n", "            break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 定位元素\n", "element = tab.ele('@id=MRoadTransportQueryCtrl1_tdMessage')\n", "\n", "if element:\n", "    # 执行 JavaScript 代码判断元素是否可见\n", "    is_visible_js = '''\n", "    var elem = arguments[0];\n", "    return (elem.offsetWidth > 0 || elem.offsetHeight > 0) && getComputedStyle(elem).visibility!== 'hidden';\n", "    '''\n", "    is_visible = tab.run_js(is_visible_js, element)\n", "\n", "    if is_visible:\n", "        print(\"元素可见。\")\n", "    else:\n", "        print(\"元素不可见。\")\n", "else:\n", "    print(\"未找到指定元素。\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# # 输入数据\n", "# for i in data:\n", "#     # 中间有空格的情况\n", "#     i = i.replace(\" \", \"\")\n", "#     if len(i) != 18:\n", "#         continue\n", "#     captcha_passed = False\n", "#     while not captcha_passed:\n", "#         tab.listen.start('credit.customs.gov.cn/ccppserver/verifyCode/creator')\n", "#         tab.get('http://credit.customs.gov.cn/')\n", "#         res = tab.listen.wait()\n", "#         # print(res.response.body)\n", "#         random_sleep_time = random.randint(2, 3)  \n", "#         time.sleep(random_sleep_time)\n", "#         # yzm 刷新\n", "#         # tab.ele('x://*[@id=\"verifyCode\"]').click()\n", "#         # 清空yzm\n", "#         tab.ele('x://*[@id=\"checkCode\"]').clear()\n", "\n", "\n", "#         input_element = tab.ele('x://*[@id=\"ID_codeName\"]') # document.querySelector(\"#ID_codeName\")\n", "#         input_element.clear()\n", "#         input_element.input(i)\n", "#         # 找到验证码截图\n", "#         # 截图并保存到本地\n", "#         # yzm = tab.ele('x://*[@id=\"verifyCode\"]')\n", "#         # result = ocr_yzm(yzm)\n", "#         result = run(res.response.body)\n", "#         tab.ele('x://*[@id=\"checkCode\"]').input(result)\n", "#         # 点击搜索按钮\n", "#         tab.ele('x://*[@id=\"ios\"]/button').click()\n", "#         # 检查是否验证码通过\n", "#         time.sleep(0.5)\n", "#         # 这里总会闪退\n", "#         # print(tab.states.is_loading)\n", "#         if tab.states.is_loading:\n", "#             # 此属性返回页面是否正在加载状态。\n", "#             tab.wait.load_start()\n", "\n", "#         if tab.url == 'http://credit.customs.gov.cn/ccppwebserver/pages/ccpp/html/copInfo.html':\n", "#             print('验证码通过',i)\n", "#             # 保存之前的图片素材\n", "#             # 移动文件到文件夹\n", "#             # for ii in range(10):\n", "#             #     shutil.move(f'yzm{ii}.jpg', f'yzmok/{result}{ii}.jpg')\n", "\n", "#             time.sleep(1)\n", "#             tab.wait.ele_displayed('x://*[@id=\"coplist\"]/div')\n", "#             tab.ele('x://*[@id=\"coplist\"]/div').click()\n", "\n", "#             ## tab.wait.ele_displayed('#copInfoForm')    # 等待元素显示\n", "#             # print(tab.ele('#copInfoForm').text()) # 获取元素文本\n", "\n", "\n", "\n", "#             tab.wait.ele_displayed('#copInfoForm',timeout=90)    # 等待元素显示\n", "#             time.sleep(1)\n", "#             if tab.url == 'http://credit.customs.gov.cn/ccppwebserver/pages/ccpp/html/detail.html':\n", "\n", "#                 # 执行JS\n", "#                 reutn_data = tab.run_js(\"\"\"\n", "#                 function extractTableData() {\n", "#                     var oldData = [];\n", "#                     var tbody = document.querySelector(\"#copInfoForm > table > tbody\");\n", "#                     var rows = tbody.querySelectorAll(\"tr\");\n", "\n", "#                     rows.forEach(function(row) {\n", "#                         var rowData = {};\n", "#                         var cells = row.querySelectorAll(\"td, th\");\n", "#                         var inputs = row.querySelectorAll(\"input\");\n", "\n", "#                         // 遍历所有单元格\n", "#                         cells.forEach(function(cell, index) {\n", "#                             var text = cell.textContent.trim();\n", "#                             if (index < inputs.length) {\n", "#                                 var input = inputs[index];\n", "#                                 if (input.type === \"checkbox\") {\n", "#                                     // 处理复选框\n", "#                                     if (input.checked) {\n", "#                                         rowData[input.id.replace('Flags', '')] = text;\n", "#                                     }\n", "#                                 } else {\n", "#                                     // 处理非复选框的输入字段\n", "#                                     rowData[text] = input.value;\n", "#                                 }\n", "#                             }\n", "#                         });\n", "\n", "#                         // 特殊处理跨境贸易电子商务类型，因为它包含多个复选框\n", "#                         var crossBorderTradeCheckboxes = row.querySelectorAll('input[type=\"checkbox\"]');\n", "#                         if (crossBorderTradeCheckboxes.length > 0) {\n", "#                             var crossBorderTradeTexts = {};\n", "#                             crossBorderTradeCheckboxes.forEach(function(checkbox) {\n", "#                                 if (checkbox.checked) {\n", "#                                     crossBorderTradeTexts[checkbox.value] = checkbox.nextSibling.textContent.trim();\n", "#                                 }\n", "#                             });\n", "#                             rowData['跨境贸易电子商务类型'] = crossBorderTradeTexts;\n", "#                         }\n", "\n", "#                         // 单独处理海关注销标志，因为它可能是单独的input元素，不与label配对\n", "#                         var revokeFlagInput = row.querySelector('input[id=\"revokeFlag\"]');\n", "#                         if (revokeFlagInput) {\n", "#                             rowData['海关注销标志'] = revokeFlagInput.value || ''; // 如果value不存在或为空，则设为空字符串\n", "#                         }\n", "\n", "#                         oldData.push(rowData);\n", "#                     });\n", "\n", "#                     var crossBorderTradeData = {};\n", "#                     // 第一次执行JS脚本提取数据\n", "#                     var crossBorderTradeElements = document.querySelectorAll(\"#regCoInfo > tbody tr\");\n", "#                     crossBorderTradeElements.forEach(function(row) {\n", "#                         var secondColumnData = row.querySelector(\"td:nth-child(2)\");\n", "#                         var text = secondColumnData.textContent.trim();\n", "#                         if ([\"跨境电子商务电商企业\", \"境外跨境电商企业的境内代理人\", \"跨境电子商务平台企业\", \"跨境电子商务物流企业\", \"跨境电子商务物流企业(仅B2B)\", \"跨境电子商务支付企业\", \"跨境电子商务监管场所运营人\"].includes(text)) {\n", "#                             crossBorderTradeData[text] = text; \n", "#                         }\n", "#                     });\n", "\n", "#                     // 第二次执行JS脚本提取数据\n", "#                     var recordDateElement = document.querySelector(\"#creditInfo > tbody > tr:nth-child(1) > td:nth-child(4)\");\n", "#                     var recordDate = null;\n", "#                     if (recordDateElement) {\n", "#                         recordDate = recordDateElement.textContent.trim();\n", "#                         // 将备案日期的格式修改为 \"2021-03-03\" 的形式\n", "#                         recordDate = recordDate.replace(/(\\d{4})(\\d{2})(\\d{2})/, \"$1-$2-$3\");\n", "#                     }\n", "\n", "#                     // 第三次执行JS脚本（此处根据你的实际需求添加）\n", "#                     // 假设我们要提取另一个元素的数据，以下为示例\n", "#                     var anotherElement = document.querySelector(\"#anotherId\");\n", "#                     var anotherData = null;\n", "#                     if (anotherElement) {\n", "#                         anotherData = anotherElement.textContent.trim();\n", "#                     }\n", "\n", "#                     // 将提取的数据依次添加到 oldData 中\n", "#                     oldData.push({\n", "#                         \"跨境贸易电子商务类型\": crossBorderTradeData\n", "#                     });\n", "#                     if (recordDate) {\n", "#                         oldData.push({\n", "#                             \"备案日期\": recordDate\n", "#                         });\n", "#                     }\n", "                    \n", "#                     oldData.push({\n", "#                         \"海关注销标志\": \"正常\"\n", "#                     });\n", "                    \n", "#                     return oldData;\n", "#                 }\n", "\n", "#                 // 调用函数并打印结果\n", "#                 var extractedData = extractTableData();\n", "#                 console.log(extractedData);\n", "#                 return extractedData;\n", "#                 \"\"\")\n", "#                 # 数据写入文本文件\n", "#                 file_path = os.path.join('数据落地', f'{i}.txt')\n", "#                 with open(file_path, 'w', encoding='utf-8') as f:\n", "#                     for item in reutn_data:\n", "#                         for key, value in item.items():\n", "#                             f.write(f\"{key}: {value}\\n\")\n", "#                 # 返回url继续爬取\n", "#                 time.sleep(0.5)\n", "#                 break\n", "\n", "#         else:\n", "#             print('验证码未通过',i)\n", "#             data_one  = tab.ele(\"x://*[contains(@class, 'layui-layer-content')]\")\n", "#             if data_one:\n", "#                 if data_one.texts()[0] == '无符合条件的数据':\n", "#                     print('无符合条件的数据')\n", "\n", "#                     file_path = os.path.join('数据落地', f'{i}_无符合条件的数据.txt')\n", "#                     with open(file_path, 'w', encoding='utf-8') as f:\n", "\n", "\n", "#                     # with open(f'{i}无符合条件的数据.txt', 'w', encoding='utf-8') as f:\n", "#                         f.write('无符合条件的数据')\n", "#                     time.sleep(0.5)\n", "#                     break\n", "#                 else:\n", "#                     tab.ele(\"x://a[contains(@class, 'layui-layer-close')]\").click()\n", "#                     random_sleep_time = random.randint(1, 2)  \n", "#                     time.sleep(random_sleep_time)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}