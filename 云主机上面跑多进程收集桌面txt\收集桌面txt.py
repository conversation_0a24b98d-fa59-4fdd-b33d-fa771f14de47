import os
import shutil

def scan_and_collect_txt_files():
    # 获取桌面路径和当前工作目录
    desktop_path = os.path.expanduser("~/Desktop")
    current_dir = os.getcwd()

    # 遍历桌面及其子目录
    for root, dirs, files in os.walk(desktop_path):
        for filename in files:
            if filename.lower().endswith('.txt'):
                file_path = os.path.join(root, filename)
                
                # 获取父文件夹名称（桌面根目录特殊处理）
                if root == desktop_path:
                    folder_name = "Desktop"
                else:
                    folder_name = os.path.basename(root)
                
                # 构建目标文件名
                base_name = f"{folder_name}_{filename}"
                dest_path = os.path.join(current_dir, filename)
                
                # 检查是否需要重命名
                if os.path.exists(dest_path):
                    # 处理可能的多次命名冲突
                    name, ext = os.path.splitext(base_name)
                    counter = 1
                    while os.path.exists(os.path.join(current_dir, f"{name}{ext}")):
                        name = f"{name.rsplit('_', 1)[0]}_{counter}"
                        dest_path = os.path.join(current_dir, f"{name}{ext}")
                        counter += 1
                    base_name = f"{name}{ext}"
                    
                # 最终目标路径
                final_dest = os.path.join(current_dir, base_name)
                
                # 复制文件并保留元数据
                shutil.copy2(file_path, final_dest)
                print(f"Copied: {file_path} -> {final_dest}")

if __name__ == "__main__":
    scan_and_collect_txt_files()