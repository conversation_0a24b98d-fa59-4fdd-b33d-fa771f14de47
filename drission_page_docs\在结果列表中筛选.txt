Title: 🔦 在结果列表中筛选
URL: https://www.drissionpage.cn/browser_control/get_elements/filter/

🔦 在结果列表中筛选
本节介绍在元素列表中按需要进行筛选，获取指定元素。
eles()、nexts()等能够获取多个元素的方法，返回的列表可进行进一步筛选，以获取指定的元素。
说明
浏览器页面对象和SessionPage产生的元素列表均有此功能，前者筛选功能比后者多。
示例1，筛选并返回元素列表：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('https://www.baidu.com')
eles = tab('#s-top-left').eles('t:a')  # 获取左上角导航栏内所有<a>元素
for ele in eles.filter.displayed():  # 筛选出显示的元素列表并逐个打印文本
    print(ele.text, end=' ')

输出：
新闻 hao123 地图 贴吧 视频 图片 网盘 文库 更多 

示例2，筛选并返回第一个元素：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('https://www.baidu.com')
eles = tab('#s-top-left').eles('t:a')  # 获取左上角导航栏内所有<a>元素
print(eles.filter_one.displayed().text)  # 筛选出显示的元素并返回第一个

输出：
新闻 

✅️️ 获取单个匹配元素​
说明
静态元素列表只有filter_one.attr()和filter_one.text()方法。
📌 filter_one.displayed()​
此方法用于在元素列表中以是否显示为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配显示的元素，False匹配不显示的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.checked()​
此方法用于在元素列表中以是否被选中为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配被选中的元素，False匹配不被选中的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.selected()​
此方法用于在元素列表中以是否被选择为条件筛选元素，返回第一个结果。用于<select>元素项目。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配被选择的元素，False匹配不被选择的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.enabled()​
此方法用于在元素列表中以是否可用为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配可用的元素，False匹配 disabled 状态的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.clickable()​
此方法用于在元素列表中以是否可点击为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配可点击的元素，False表示匹配不是可点击的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.have_rect()​
此方法用于在元素列表中以是否有大小为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配有大小的元素，False表示匹配没有大小的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.style()​
此方法用于在元素列表中以是否拥有某个 style 值为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.property()​
此方法用于在元素列表中以是否拥有某个 property 值为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.attr()​
此方法用于在元素列表中以是否拥有某个 attribute 值为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.text()​
此方法用于在元素列表中以是否含有指定文本为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
text	str	必填	用于匹配的文本
fuzzy	bool	True	是否模糊匹配
contain	bool	True	是否包含该字符串，False表示不包含
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 filter_one.tag()​
此方法用于在元素列表中以某个类型为条件筛选元素，返回第一个结果。
参数名称	类型	默认值	说明
name	str	必填	元素类型名称
equal	bool	True	True表示匹配该类型元素，False表示匹配非该类型元素
返回类型	说明
ChromiumElement	匹配成功返回元素对象
NoneElement	失败返回NoneElement
抛出ElementNotFoundError异常	Settings.raise_when_ele_not_found为True时抛出
📌 选择获取第几个​
filter_one可加参数，以选择返回第几个结果。
示例：
ele = eles.filter_one(2).text('图')  # 获取第二个文本带有“图”字的元素

说明
filter_one在不加序号参数时，可不要后面的()。
✅️️ 获取全部匹配元素​
说明
静态元素列表只有filter.attr()和filter.text()方法。
📌 filter.displayed()​
此方法用于在元素列表中以是否显示为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配显示的元素，False匹配不显示的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.checked()​
此方法用于在元素列表中以是否被选中为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配被选中的元素，False匹配不被选中的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.selected()​
此方法用于在元素列表中以是否被选择为条件筛选元素，返回新的列表。用于<select>元素项目。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配被选择的元素，False匹配不被选择的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.enabled()​
此方法用于在元素列表中以是否可用为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配可用的元素，False匹配 disabled 状态的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.clickable()​
此方法用于在元素列表中以是否可点击为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配可点击的元素，False表示匹配不是可点击的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.have_rect()​
此方法用于在元素列表中以是否有大小为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
equal	bool	True	是否匹配有大小的元素，False表示匹配没有大小的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.style()​
此方法用于在元素列表中以是否拥有某个 style 值为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.property()​
此方法用于在元素列表中以是否拥有某个 property 值为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.tag()​
此方法用于在元素列表中以某个类型为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
name	str	必填	元素类型名称
equal	bool	True	True表示匹配该类型元素，False表示匹配非该类型元素
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.attr()​
此方法用于在元素列表中以是否拥有某个 attribute 值为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
name	str	必填	属性名称
value	str	必填	属性值
equal	bool	True	True表示匹配name值为value值的元素，False表示匹配name值不为value值的
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
📌 filter.text()​
此方法用于在元素列表中以是否含有指定文本为条件筛选元素，返回新的列表。
参数名称	类型	默认值	说明
text	str	必填	用于匹配的文本
fuzzy	bool	True	是否模糊匹配
contain	bool	True	是否包含该字符串，False表示不包含
返回类型	说明
Filter	元素对象组成的列表，可继续用于筛选
✅️️ 多条件筛选​
📌 与关系筛选​
筛选支持链式操作，可串连多个条件，每个条件会筛选一层再进入下一层。
可实现多个条件的与关系筛选。
示例，出导航栏中显示且含有“图”字的元素：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('https://www.baidu.com')
eles = tab('#s-top-left').eles('t:a')
for ele in eles.filter.displayed().text('图'):
    print(ele.text, end=' ')

📌 或关系筛选​
元素列表的search()和search_one()方法可用于多个条件或筛选元素。
说明
静态元素列表没有这种方法。
参数名称	类型	默认值	说明
index（search_one()独有）	int	1	结果中的元素序号，1开始
displayed	bool	None	是否显示，bool表示匹配是或否，None为忽略该项
checked	bool	None	是否被选中，bool表示匹配是或否，None为忽略该项
selected	bool	None	是否被选择，bool表示匹配是或否，None为忽略该项
enabled	bool	None	是否可用，bool表示匹配是或否，None为忽略该项
clickable	bool	None	是否可点击，bool表示匹配是或否，None为忽略该项
have_rect	bool	None	是否拥有大小，bool表示匹配是或否，None为忽略该项
have_text	bool	None	是否含有文本，bool表示匹配是或否，None为忽略该项
tag	str	None	指定标签页类型，None为忽略该项
返回类型	说明
Filter	search()返回元素对象组成的列表
ChromiumElement	search_one()匹配成功返回元素对象
NoneElement	search_one()匹配失败返回NoneElement
📌 混合筛选​
与关系和或关系可以用链式操作混合使用。
说明
静态元素列表没有这种方法。
es = eles.search(displayed=True).enabled()
ele = eles.filter.enablde().search_one(displayed=True)

✅️️ 获取筛选结果的属性集合​
筛选结果列表可以调用get()方法获取指定属性结合。
该集合为遍历列表中所有元素获取的。
示例：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('https://www.baidu.com')
eles = tab('#s-top-left').eles('t:a')
print(eles.get.texts())  # 获取所有元素的文本
print(eles.filter.displayed().get.texts())  # 获取的元素的文本

输出：
['新闻', 'hao123', '地图', '贴吧', '视频', '图片', '网盘', '文库', '更多', '翻译', '学术', '百科', '知道', '健康', '营销推广', '直播', '音乐', '橙篇', '查看全部百度产品 >']
['新闻', 'hao123', '地图', '贴吧', '视频', '图片', '网盘', '文库', '更多']

📌 get.attrs()​
此方法用于返回所有元素指定的 attribute 属性组成的列表。
参数名称	类型	默认值	说明
name	str	必填	属性名称
返回类型	说明
List[str]	属性文本组成的列表
📌 get.links()​
此方法用于返回所有元素的link属性组成的列表。
参数： 无
返回类型	说明
List[str]	链接文本组成的列表
📌 get.texts()​
此方法用于返回所有元素的text属性组成的列表。
参数： 无
返回类型	说明
List[str]	元素文本组成的列表