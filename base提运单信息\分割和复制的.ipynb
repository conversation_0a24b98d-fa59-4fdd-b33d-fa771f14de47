{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已将文件分割为 33 个部分，每个部分最多 200 条数据\n"]}], "source": ["import shutil\n", "import os\n", "import pandas as pd\n", "\n", "def split_csv(input_file=None, output_base_dir='分割后的文件目录', chunk_size=200, script_file=None):\n", "    \"\"\"\n", "    将CSV文件分割成多个小文件，并复制处理脚本到各个子目录\n", "    \n", "    参数:\n", "    input_file: 输入CSV文件路径，如果为None则自动查找当前目录下的CSV文件\n", "    output_base_dir: 输出目录基础路径\n", "    chunk_size: 每个分割文件的最大行数\n", "    script_file: 要复制的处理脚本路径，如果为None则自动查找当前目录下的提运单信息.py\n", "    \"\"\"\n", "    # 如果未指定输入文件，则查找当前目录下的CSV文件\n", "    if input_file is None:\n", "        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]\n", "        if csv_files:\n", "            input_file = csv_files[0]\n", "            print(f\"自动选择CSV文件: {input_file}\")\n", "        else:\n", "            print(\"错误: 当前目录下未找到CSV文件\")\n", "            return\n", "    \n", "    # 如果未指定处理脚本，则查找当前目录下的提运单信息.py\n", "    if script_file is None:\n", "        if os.path.exists('提运单信息.py'):\n", "            script_file = '提运单信息.py'\n", "            print(f\"自动选择处理脚本: {script_file}\")\n", "        else:\n", "            print(\"错误: 未找到提运单信息.py文件\")\n", "            return\n", "    \n", "    # 确保基础输出目录存在\n", "    if not os.path.exists(output_base_dir):\n", "        os.makedirs(output_base_dir)\n", "    \n", "    # 读取原始CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 计算需要分割的文件数量\n", "    total_rows = len(df)\n", "    num_files = (total_rows // chunk_size) + (1 if total_rows % chunk_size else 0)\n", "    \n", "    # 分割并保存文件\n", "    for i in range(num_files):\n", "        # 为每个分割文件创建单独的文件夹\n", "        part_dir = os.path.join(output_base_dir, f'part_{i+1}')\n", "        os.makedirs(part_dir, exist_ok=True)\n", "        \n", "        # 分割数据\n", "        start_idx = i * chunk_size\n", "        end_idx = start_idx + chunk_size\n", "        chunk = df.iloc[start_idx:end_idx]\n", "        \n", "        # 保存分割文件\n", "        output_file = os.path.join(part_dir, f'part_{i+1}.csv')\n", "        chunk.to_csv(output_file, index=False)\n", "        \n", "        # 复制处理脚本到每个文件夹\n", "        shutil.copy(script_file, part_dir)\n", "    \n", "    print(f'已将文件分割为 {num_files} 个部分，每个部分最多 {chunk_size} 条数据')\n", "\n", "# 使用示例 - 使用默认参数，自动查找文件\n", "split_csv()\n", "\n", "# 如果需要指定文件路径，可以取消下面的注释并修改路径\n", "# split_csv(\n", "#     input_file='跨境清单批次号.csv',\n", "#     output_base_dir='分割后的文件目录',\n", "#     script_file='提运单信息.py'\n", "# )"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文件合并完成！\n"]}], "source": ["import os\n", "\n", "def merge_txt_files(input_dir=None, output_file=None):\n", "    \"\"\"\n", "    合并指定目录下的所有TXT文件到一个文件中\n", "    \n", "    参数:\n", "    input_dir: 输入目录，包含要合并的TXT文件，如果为None则使用'数据落地'\n", "    output_file: 输出文件路径，如果为None则使用'base提运单信息.txt'\n", "    \"\"\"\n", "    # 设置默认值\n", "    if input_dir is None:\n", "        # 查找当前目录下可能的数据目录\n", "        possible_dirs = ['数据落地', '20250410']\n", "        for dir_name in possible_dirs:\n", "            if os.path.exists(dir_name):\n", "                input_dir = dir_name\n", "                print(f\"自动选择输入目录: {input_dir}\")\n", "                break\n", "        \n", "        if input_dir is None:\n", "            print(\"错误: 未找到默认数据目录，请手动指定input_dir参数\")\n", "            return\n", "    \n", "    if output_file is None:\n", "        output_file = 'base提运单信息.txt'\n", "        print(f\"使用默认输出文件: {output_file}\")\n", "    \n", "    # 确保输入目录存在\n", "    if not os.path.exists(input_dir):\n", "        print(f\"错误: 输入目录不存在: {input_dir}\")\n", "        return\n", "    \n", "    # 创建输出文件的目录（如果不存在）\n", "    output_dir = os.path.dirname(output_file)\n", "    if output_dir and not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    \n", "    with open(output_file, 'w', encoding='utf-8') as outfile:\n", "        # 遍历所有子目录\n", "        for root, _, files in os.walk(input_dir):\n", "            # 按文件名排序后处理\n", "            for filename in sorted(files):\n", "                if filename.endswith('.txt'):\n", "                    filepath = os.path.join(root, filename)\n", "                    try:\n", "                        # 读取并写入内容，自动添加换行分隔\n", "                        with open(filepath, 'r', encoding='utf-8') as infile:\n", "                            outfile.write(infile.read() + '\\n')\n", "                    except Exception as e:\n", "                        print(f\"跳过无法读取的文件: {filepath} ({str(e)})\")\n", "\n", "    print(f\"文件合并完成！已将所有TXT文件合并到: {output_file}\")\n", "\n", "# 使用示例 - 使用默认参数\n", "merge_txt_files()\n", "\n", "# 如果需要指定路径，可以取消下面的注释并修改路径\n", "# merge_txt_files(\n", "#     input_dir='20250410',\n", "#     output_file='base提运单信息.txt'\n", "# )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}