Title: 🔦 语法速查表
URL: https://www.drissionpage.cn/browser_control/get_elements/sheet/

🔦 语法速查表
云服务器38元/年起，大模型限免超7000万 tokens广告


✅️ 定位语法​
📌 基本用法​
写法	精确匹配	模糊匹配	匹配开头	匹配结尾	说明
@属性名	@属性名=	@属性名:	@属性名^	@属性名$	按某个属性查找
@!属性名	@!属性名=	@!属性名:	@!属性名^	@!属性名$	查找属性不符合指定条件的元素
text	text=	text:或不写	text^	text$	按某个文本查找
@text()	@text()=	@text():	text()^	text()$	text与@或@@配合使用时改为text()，常用于多条件匹配
tag	tag=或tag:	无	无	无	查找某个类型的元素
@tag()	@tag()=或@tag():	无	无	无	组合使用时查找某个类型的元素
xpath	xpath=或xpath:	无	无	无	用 xpath 方式查找元素
css	css=或css:	无	无	无	用 css selector 方式查找元素
📌 组合用法​
写法	说明
@@属性1@@属性2	匹配属性同时符合多个条件的元素
@@属性1@!属性2	多属性匹配与否定匹配同时使用
@|属性1@|属性2	匹配属性至符合多个条件中一的元素
tag:元素名@属性名	tag与属性匹配共同使用
tag:元素名@@属性1@@属性2	tag与多属性匹配共同使用
tag:元素名@|属性1@|属性2	tag与多属性匹配共同使用
tag:元素名@@text()=文本@@属性	tag与文本和属性匹配共同使用
📌 简化写法​
原写法	简化写法	精确匹配	模糊匹配	匹配开头	匹配结尾	备注
@id	#	#或#=	#:	#^	#$	简化写法只能单独使用
@class	.	.或.=	.:	.^	.$	简化写法只能单独使用
tag	t	t:或t=	无	无	无	只能用在句首
@tag()	@t()	@t():或@t()=	无	无	无	可作为属性组合使用
text	tx	tx=	tx:或不写	tx^	tx$	无标签时使用模糊匹配文本
@text()	@tx()	@tx()=	@tx():	@tx()^	@tx()$	可作为属性组合使用
xpath	x	x:或x=	无	无	无	只能单独使用
css	c	c:或c=	无	无	无	只能单独使用
✅️ 相对定位​
方法	说明
parent()	查找当前元素某一级父元素
child()	查找当前元素的一个直接子节点
children()	查找当前元素全部符合条件的直接子节点
next()	查找当前元素之后第一个符合条件的兄弟节点
nexts()	查找当前元素之后所有符合条件的兄弟节点
prev()	查找当前元素之前第一个符合条件的兄弟节点
prevs()	查找当前元素之前所有符合条件的兄弟节点
after()	查找文档中当前元素之后第一个符合条件的节点
afters()	查找文档中当前元素之后所有符合条件的节点
before()	查找文档中当前元素之前第一个符合条件的节点
befores()	查找文档中当前元素之前所有符合条件的节点
✅️ iframe 和 shadow root​
方法	简化写法	说明	备注
get_frame()	无	在页面中查找一个<iframe>元素	只有页面对象有此方法
shadow_root	sr	获取当前元素内的 shadow root 对象	只有元素对象有此属性
✅️ 特殊字符对照表​
要匹配的文本中如包含特殊字符（如' '、'>'），需将特殊字符转为十六进制，对照表如下：
特殊符号	命名实体	编码	特殊符号	命名实体	编码	特殊符号	命名实体	编码
Α	Α	\u0391	Β	Β	\u0392	Γ	Γ	\u0393
Δ	Δ	\u0394	Ε	Ε	\u0395	Ζ	Ζ	\u0396
Η	Η	\u0397	Θ	Θ	\u0398	Ι	Ι	\u0399
Κ	Κ	\u039A	Λ	Λ	\u039B	Μ	Μ	\u039C
Ν	Ν	\u039D	Ξ	Ξ	\u039E	Ο	Ο	\u039F
Π	Π	\u03A0	Ρ	Ρ	\u03A1	Σ	Σ	\u03A3
Τ	Τ	\u03A4	Υ	Υ	\u03A5	Φ	Φ	\u03A6
Χ	Χ	\u03A7	Ψ	Ψ	\u03A8	Ω	Ω	\u03A9
α	α	\u03B1	β	β	\u03B2	γ	γ	\u03B3
δ	δ	\u03B4	ε	ε	\u03B5	ζ	ζ	\u03B6
η	η	\u03B7	θ	θ	\u03B8	ι	ι	\u03B9
κ	κ	\u03BA	λ	λ	\u03BB	μ	μ	\u03BC
ν	ν	\u03BD	ξ	ξ	\u03BE	ο	ο	\u03BF
π	π	\u03C0	ρ	ρ	\u03C1	ς	ς	\u03C2
σ	σ	\u03C3	τ	τ	\u03C4	υ	υ	\u03C5
φ	φ	\u03C6	χ	χ	\u03C7	ψ	ψ	\u03C8
ω	ω	\u03C9	ϑ	ϑ	\u03D1	ϒ	ϒ	\u03D2
ϖ	ϖ	\u03D6	•	•	\u2022	…	…	\u2026
′	′	\u2032	″	″	\u2033	‾	‾	\u203E
⁄	⁄	\u2044	℘	℘	\u2118	ℑ	ℑ	\u2111
ℜ	ℜ	\u211C	™	™	\u2122	ℵ	ℵ	\u2135
←	←	\u2190	↑	↑	\u2191	→	→	\u2192
↓	↓	\u2193	↔	↔	\u2194	↵	↵	\u21B5
⇐	⇐	\u21D0	⇑	⇑	\u21D1	⇒	⇒	\u21D2
⇓	⇓	\u21D3	⇔	⇔	\u21D4	∀	∀	\u2200
∂	∂	\u2202	∃	∃	\u2203	∅	∅	\u2205
∇	∇	\u2207	∈	∈	\u2208	∉	∉	\u2209
∋	∋	\u220B	∏	∏	\u220F	∑	∑	\u2212
−	−	\u2212	∗	∗	\u2217	√	√	\u221A
∝	∝	\u221D	∞	∞	\u221E	∠	∠	\u2220
∧	∧	\u22A5	∨	∨	\u22A6	∩	∩	\u2229
∪	∪	\u222A	∫	∫	\u222B	∴	∴	\u2234
∼	∼	\u223C	≅	≅	\u2245	≈	≈	\u2245
≠	≠	\u2260	≡	≡	\u2261	≤	≤	\u2264
≥	≥	\u2265	⊂	⊂	\u2282	⊃	⊃	\u2283
⊄	⊄	\u2284	⊆	⊆	\u2286	⊇	⊇	\u2287
⊕	⊕	\u2295	⊗	⊗	\u2297	⊥	⊥	\u22A5
⋅	⋅	\u22C5	⌈	⌈	\u2308	⌉	⌉	\u2309
⌊	⌊	\u230A	⌋	⌋	\u230B	◊	◊	\u25CA
♠	♠	\u2660	♣	♣	\u2663	♥	♥	\u2665
♦	♦	\u2666	 	 	\u00A0	¡	¡	\u00A1
¢	¢	\u00A2	£	£	\u00A3	¤	¤	\u00A4
¥	¥	\u00A5	¦	¦	\u00A6	§	§	\u00A7
¨	¨	\u00A8	©	©	\u00A9	ª	ª	\u00AA
«	«	\u00AB	¬	¬	\u00AC	­	­	\u00AD
®	®	\u00AE	¯	¯	\u00AF	°	°	\u00B0
±	±	\u00B1	²	²	\u00B2	³	³	\u00B3
´	´	\u00B4	µ	µ	\u0012	"	"	\u0022
<	<	\u003C	>	>	\u003E	'	 	\u0027