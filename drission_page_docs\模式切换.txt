Title: 🛰️ 模式切换
URL: https://www.drissionpage.cn/browser_control/mode_change/

🛰️ 模式切换


MixTab和WebPage有两种模式，d 模式用于控制浏览器，s 模式使用requests收发数据包。
两种模式访问页面和提取数据的逻辑是一致的，使用同一套 api。
每个标签页对象创建时都处于 d 模式。
使用change_mode()方法进行切换。模式切换的时候会同步登录信息。
s 模式下仍然可以控制浏览器，但因为共用 api，ele()等两种模式共用的方法，查找对象是requests的结果，而非浏览器。
因此 s 模式下要控制浏览器，只能调用 d 模式独有的功能。
在切换模式前已获取的元素对象则可继续操作。
Tips
切换到 s 模式后，如不再需要浏览器，可以用close()或quit()方法关闭标签页或浏览器。标签页对象继续用于收发数据包。
✅️️ 示例​
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('http://DrissionPage.cn')
print(tab.title)  # 打印d模式下网页title
tab.change_mode()  # 切换到s模式，切换时会自动访问d模式的url
print(tab.title)  # 打印s模式下网页title

输出：
DrissionPage官网
DrissionPage官网

✅️️ 相关属性和方法​
📌️ mode​
此属性返回当前模式。'd'或's'。
类型：str
📌 change_mode()​
此方法用于切换运行模式。
切换模式时默认复制当前 cookies 到目标模式，且使用当前 url 进行跳转。
注意
切换模式时只同步 cookies，不同步 headers，如果网站要求特定的 headers 才能访问，就会卡住直到超时。 这时可以设置go为False，切换 s 模式后再自己构造 headers 访问。
参数名称	类型	默认值	说明
mode	str
None	None	接收's'或'd'，以切换到指定模式
接收None则切换到与当前相对的另一个模式
go	bool	True	目标模式是否跳转到原模式的 url
copy_cookies	bool	True	切换时是否复制 cookies 到目标模式
返回：None
📌 cookies_to_session()​
此方法用于复制浏览器当前页面的 cookies 到Session对象。
参数名称	类型	默认值	说明
copy_user_agent	bool	True	是否复制 user agent 信息
返回：None
📌 cookies_to_browser()​
此方法用于把Session对象的 cookies 复制到浏览器。
参数： 无
返回：None
✅️️ 说明​
主要的 api 两种模式是共用的，如get()，d 模式下控制浏览跳转，s 模式下控制Session对象跳转
s 模式下获取的元素对象为SessionElement，d 模式下为ChromiumElement等
post()方法无论在哪种模式下都能使用
s 模式下也能控制浏览器，但只能使用 d 模式独有功能控制