# -*- coding: utf-8 -*-
"""
测试提运单信息查询系统
"""

import pandas as pd
from api_cracker import CustomsAPICracker
import time

def test_road_query_system():
    """测试提运单信息查询系统"""
    print("=== 测试提运单信息查询系统 ===")
    
    # 读取一些批次号进行测试
    df = pd.read_csv('202502190824.csv')
    test_ids = df.iloc[:10, 0].astype(str).tolist()  # 取前10个进行测试
    
    print(f"测试批次号: {test_ids}")
    
    cracker = CustomsAPICracker(max_workers=3)
    
    print("\n开始测试提运单信息查询...")
    results = cracker.batch_query(
        manifest_ids=test_ids,
        query_type='road_query',  # 使用提运单信息查询
        output_file='road_query_test_results'
    )
    
    # 分析结果
    successful = [r for r in results if r['status'] == 'success']
    no_record = [r for r in results if r['status'] == 'no_record']
    errors = [r for r in results if r['status'] not in ['success', 'no_record']]
    
    print(f"\n=== 提运单信息查询结果分析 ===")
    print(f"总查询数: {len(results)}")
    print(f"成功查询: {len(successful)}")
    print(f"无记录查询: {len(no_record)}")
    print(f"错误查询: {len(errors)}")
    print(f"验证码绕过: {cracker.stats['captcha_bypassed']}")
    
    if successful:
        print(f"\n成功查询示例:")
        for i, result in enumerate(successful[:3]):  # 显示前3个成功的结果
            print(f"  批次号 {result['manifest_id']}: {len(result['data'])} 条记录")
            if result['data']:
                print(f"    示例数据: {result['data'][0]}")
    
    if errors:
        print(f"\n错误查询详情:")
        for error in errors:
            print(f"  批次号 {error['manifest_id']}: {error['status']}")
            if 'error' in error:
                print(f"    错误信息: {error['error']}")
    
    return results

def compare_two_systems():
    """比较两个查询系统的效果"""
    print("\n=== 比较两个查询系统 ===")
    
    # 使用相同的批次号测试两个系统
    df = pd.read_csv('202502190824.csv')
    test_ids = df.iloc[:5, 0].astype(str).tolist()  # 取前5个进行对比测试
    
    cracker = CustomsAPICracker(max_workers=1)
    
    print(f"对比测试批次号: {test_ids}")
    
    # 测试公路舱单查询
    print(f"\n--- 公路舱单查询结果 ---")
    road_transport_results = []
    for manifest_id in test_ids:
        result = cracker.query_single(manifest_id, 'road_transport')
        road_transport_results.append(result)
        print(f"批次号 {manifest_id}: {result['status']}")
        if result['status'] == 'success' and result['data']:
            print(f"  数据条数: {len(result['data'])}")
        time.sleep(1)
    
    # 测试提运单信息查询
    print(f"\n--- 提运单信息查询结果 ---")
    road_query_results = []
    for manifest_id in test_ids:
        result = cracker.query_single(manifest_id, 'road_query')
        road_query_results.append(result)
        print(f"批次号 {manifest_id}: {result['status']}")
        if result['status'] == 'success' and result['data']:
            print(f"  数据条数: {len(result['data'])}")
        time.sleep(1)
    
    # 对比分析
    print(f"\n--- 对比分析 ---")
    
    rt_success = len([r for r in road_transport_results if r['status'] == 'success'])
    rt_no_record = len([r for r in road_transport_results if r['status'] == 'no_record'])
    rt_errors = len([r for r in road_transport_results if r['status'] not in ['success', 'no_record']])
    
    rq_success = len([r for r in road_query_results if r['status'] == 'success'])
    rq_no_record = len([r for r in road_query_results if r['status'] == 'no_record'])
    rq_errors = len([r for r in road_query_results if r['status'] not in ['success', 'no_record']])
    
    print(f"公路舱单查询 - 成功: {rt_success}, 无记录: {rt_no_record}, 错误: {rt_errors}")
    print(f"提运单信息查询 - 成功: {rq_success}, 无记录: {rq_no_record}, 错误: {rq_errors}")
    
    # 检查是否有相同批次号在两个系统中都有数据
    for i, manifest_id in enumerate(test_ids):
        rt_result = road_transport_results[i]
        rq_result = road_query_results[i]
        
        if rt_result['status'] == 'success' and rq_result['status'] == 'success':
            print(f"\n批次号 {manifest_id} 在两个系统中都有数据:")
            print(f"  公路舱单: {len(rt_result['data'])} 条记录")
            print(f"  提运单信息: {len(rq_result['data'])} 条记录")

def test_captcha_bypass_both_systems():
    """测试两个系统的验证码绕过"""
    print("\n=== 测试两个系统的验证码绕过 ===")
    
    import requests
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    })
    
    # 测试验证码URL
    captcha_url = 'http://query.customs.gov.cn/MNFTQ/Image.aspx'
    
    print(f"测试验证码URL: {captcha_url}")
    
    for i in range(5):
        print(f"\n第 {i+1} 次测试:")
        try:
            response = session.get(captcha_url)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                cookies = dict(response.cookies)
                print(f"  Cookies: {cookies}")
                
                # 检查验证码答案
                verify_code = response.cookies.get('Verify') or response.cookies.get('VerfyRoadCode')
                if verify_code:
                    print(f"  验证码答案: {verify_code}")
                    print(f"  验证码绕过: ✓ 成功")
                else:
                    print(f"  验证码绕过: ✗ 失败")
            
        except Exception as e:
            print(f"  请求失败: {e}")
        
        time.sleep(1)

def main():
    print("提运单信息查询系统测试")
    print("="*50)
    
    # 1. 测试验证码绕过
    test_captcha_bypass_both_systems()
    
    # 2. 测试提运单信息查询系统
    test_road_query_system()
    
    # 3. 比较两个系统
    compare_two_systems()

if __name__ == "__main__":
    main()
