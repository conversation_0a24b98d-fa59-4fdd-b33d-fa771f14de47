Title: 🛰️ 获取元素信息
URL: https://www.drissionpage.cn/browser_control/get_ele_info/

🛰️ 获取元素信息
云服务器38元/年起，大模型限免超7000万 tokens广告


浏览器元素对应的对象是ChromiumElement和ShadowRoot，本节介绍如何获取元素信息。
✅️️ 内容和属性​
📌 tag​
此属性返回元素的标签名。
返回类型：str
📌 html​
此属性返回元素的outerHTML文本。
返回类型：str
📌 inner_html​
此属性返回元素的innerHTML文本。
返回类型：str
📌 text​
此属性返回元素内所有文本组合成的字符串。
该字符串已格式化，即已转码，已去除多余换行符，符合人读取习惯，便于直接使用。
返回类型：str
📌 raw_text​
此属性返回元素内未经处理的原始文本。
返回类型：str
📌 texts()​
此方法返回元素内所有直接子节点的文本，包括元素和文本节点。 它有一个参数text_node_only，为True时则只获取只返回不被包裹的文本节点。这个方法适用于获取文本节点和元素节点混排的情况。
参数名称	类型	默认值	说明
text_node_only	bool	False	是否只返回文本节点
返回类型	说明
List[str]	文本列表
📌 comments​
此属性以列表形式返回元素内的注释。
返回类型：List[str]
📌 attrs​
此属性以字典形式返回元素所有属性及值。
返回类型：dict
📌 attr()​
此方法返回元素某个 attribute 属性值。它接收一个字符串参数，返回该属性值文本，无该属性时返回None。
此属性返回的src、href属性为已补充完整的路径。text属性为已格式化文本。 如果要获取未补充完整路径的src或href属性，可以用attrs['src']。
参数名称	类型	默认值	说明
name	str	必填	属性名称
返回类型	说明
str	属性值文本
None	没有该属性返回None
📌 property()​
此方法返回property属性值。它接收一个字符串参数，返回该参数的属性值。
参数名称	类型	默认值	说明
name	str	必填	属性名称
返回类型	说明
str	属性值
📌 value​
此方法返回元素的value值。
返回类型：str
📌 link​
此方法返回元素的href属性或src属性，没有这两个属性则返回None。
返回类型：str
📌 pseudo.before​
此属性以文本形式返回当前元素的::before伪元素内容。
类型：str
📌 pseudo.after​
此属性以文本形式返回当前元素的::after伪元素内容。
类型：str
📌 style()​
该方法返回元素 css 样式属性值，可获取伪元素的属性。它有两个参数，style参数输入样式属性名称，pseudo_ele 参数输入伪元素名称，省略则获取普通元素的 css 样式属性。
参数名称	类型	默认值	说明
style	str	必填	样式名称
pseudo_ele	str	''	伪元素名称（如有）
返回类型	说明
str	样式属性值
📌 shadow_root​
此属性返回元素内的 shadow-root 对象，没有的返回None。
类型：ShadowRoot
📌 child_count​
此属性返回元素内第一级子元素个数。
类型：int
✅️️ 大小和位置​
📌 rect.size​
此属性以元组形式返回元素的大小。
类型：Tuple[float, float]
size = ele.rect.size
# 返回：(50, 50)

📌 rect.location​
此属性以元组形式返回元素左上角在整个页面中的坐标。
类型：Tuple[float, float]
loc = ele.rect.location
# 返回：(50, 50)

📌 rect.midpoint​
此属性以元组形式返回元素中点在整个页面中的坐标。
类型：Tuple[float, float]
loc = ele.rect.midpoint
# 返回：(55, 55)

📌 rect.click_point​
此属性以元组形式返回元素点击点在整个页面中的坐标。
点击点是指click()方法点击时的位置，位于元素中上部。
类型：Tuple[float, float]
📌 rect.corners​
此属性以列表形式返回元素四个角在页面中的坐标，顺序：左上、右上、右下、左下。
类型：((float, float), (float, float), (float, float), (float, float),)
📌 rect.viewport_corners​
此属性以列表形式返回元素四个角在视口中的坐标，顺序：左上、右上、右下、左下。
类型：list[(float, float), (float, float), (float, float), (float, float)]
📌 rect.viewport_location​
此属性以元组形式返回元素左上角在当前视口中的坐标。
类型：Tuple[float, float]
📌 rect.viewport_midpoint​
此属性以元组形式返回元素中点在当前视口中的坐标。
类型：Tuple[floatt, float]
📌 rect.viewport_click_point​
此属性以元组形式返回元素点击点在当前视口中的坐标。
类型：Tuple[float, float]
📌 rect.screen_location​
此属性以元组形式返回元素左上角在屏幕中的坐标。
类型：Tuple[float, float]
📌 rect.screen_midpoint​
此属性以元组形式返回元素中点在屏幕中的坐标。
类型：Tuple[float, float]
📌 rect.screen_click_point​
此属性以元组形式返回元素点击点在屏幕中的坐标。
类型：Tuple[float, float]
📌 rect.scroll_position​
此属性返回元素内滚动条位置，格式：(x, y)。
类型：Tuple[float, float]
📌 xpath​
此属性返回当前元素在页面中 xpath 的绝对路径。
返回类型：str
📌 css_path​
此属性返回当前元素在页面中 css selector 的绝对路径。
返回类型：str
✅️️ 元素列表中批量获取信息​
eles()等返回的元素列表，自带get属性，可用于获取指定信息。
📌 示例​
from DrissionPage import SessionPage

page = SessionPage()
page.get('https://www.baidu.com')
eles = page('#s-top-left').eles('t:a')
print(eles.get.texts())  # 获取所有元素的文本

输出：
['新闻', 'hao123', '地图', '贴吧', '视频', '图片', '网盘', '文库', '更多', '翻译', '学术', '百科', '知道', '健康', '营销推广', '直播', '音乐', '橙篇', '查看全部百度产品 >']

📌 get.attrs()​
此方法用于返回所有元素指定的 attribute 属性组成的列表。
参数名称	类型	默认值	说明
name	str	必填	属性名称
返回类型	说明
List[str]	属性文本组成的列表
📌 get.links()​
此方法用于返回所有元素的link属性组成的列表。
参数： 无
返回类型	说明
List[str]	链接文本组成的列表
📌 get.texts()​
此方法用于返回所有元素的text属性组成的列表。
参数： 无
返回类型	说明
List[str]	元素文本组成的列表
✅️️ 状态信息​
📌timeout​
此属性返回获取内部或相对定位元素的超时时间，实际上是元素所在页面的超时设置。
类型：float
📌states.is_in_viewport​
此属性以布尔值方式返回元素是否在视口中，以元素可以接受点击的点为判断。
类型：bool
📌states.is_whole_in_viewport​
此属性以布尔值方式返回元素是否整个在视口中。
类型：bool
📌states.is_alive​
此属性以布尔值形式返回当前元素是否仍可用。用于判断 d 模式下是否因页面刷新而导致元素失效。
类型：bool
📌 states.is_checked​
此属性以布尔值返回表单单选或多选元素是否选中。
类型：bool
📌 states.is_selected​
此属性以布尔值返回<select>元素中的项是否选中。
类型：bool
📌 states.is_enabled​
此属性以布尔值返回元素是否可用。
类型：bool
📌 states.is_displayed​
此属性以布尔值返回元素是否可见。
类型：bool
📌 states.is_covered​
此属性返回元素是否被其它元素覆盖。如被覆盖，返回覆盖元素的 id，否则返回False
返回类型	说明
False	未被覆盖，返回False
int	被覆盖时返回覆盖元素的 id
📌 states.is_clickable​
此属性返回元素是否可被模拟点击，从是否有大小、是否可用、是否显示、是否响应点击判断，不判断是否被遮挡。
类型：bool
📌 states.has_rect​
此属性返回元素是否拥有大小和位置信息，有则返回四个角在页面上的坐标组成的列表，没有则返回False。
返回类型	说明
list	存在大小和位置信息时，以[(int, int), ...] 格式返回元素四个角的坐标，顺序：左上、右上、右下、左下
False	不存在时返回False
✅️️ 保存元素​
保存功能是本库一个特色功能，可以直接读取浏览器缓存，无需依赖另外的 ui 库或重新下载就可以保存页面资源。
作为对比，selenium 无法自身实现图片另存，往往需要通过使用 ui 工具进行辅助，不仅效率和可靠性低，还占用键鼠资源。
📌 src()​
此方法用于返回元素src属性所使用的资源。base64 的可转为bytes返回，其它的以str返回。无资源的返回None。
例如，可获取页面上图片字节数据，用于识别内容，或保存到文件。<script>和<link>标签也可获取文件内容。
注意
获取<script>或<link>文件内容时，视网站情况不一定会成功。
参数名称	类型	默认值	说明
timeout	float	None	等待资源加载超时时间（秒），为None时使用元素所在页面timeout属性
base64_to_bytes	bool	True	为True时，如果是 base64 数据，转换为bytes格式
返回类型	说明
str	资源字符串
None	无资源的返回None
示例：
img = page('tag:img')
src = img.src()

📌 save()​
此方法用于保存src()方法获取到的资源到文件。
参数名称	类型	默认值	说明
path	str
Path	None	文件保存路径，为None时保存到当前文件夹
name	str	None	文件名称，需包含后缀，为None时从资源 url 获取
timeout	float	None	等待资源加载超时时间（秒），为None时使用元素所在页面timeout属性
rename	bool	True	遇到重名文件时是否自动重命名
返回类型	说明
str	保存路径
示例：
img = page('tag:img')
img.save('D:\\img.png')

✅️️ 比较元素​
两个元素对象可以用==来比较，以判断它们是否指向同一个元素。
示例：
ele1 = page('t:div')
ele2 = page('t:div')
print(ele1==ele2)  # 输出True