Title: 🛰️ 浏览器对象
URL: https://www.drissionpage.cn/browser_control/browser_object/

🛰️ 浏览器对象
云服务器38元/年起，大模型限免超7000万 tokens广告


我们已经了解如何创建浏览器对象，本节介绍浏览器对象的功能。
说明
文中的 “Tab 对象” 是ChromiumTab和MixTab的统称。
✅️️ 获取标签页对象或信息​
📌 get_tab()​
此方法用于获取一个标签页对象或它的 id。
id_or_num不为None时，获取id_or_num指定的标签页。后面几个参数无效。
id_or_num为None时，根据后面几个参数指定的条件查找标签页（与关系）。
参数名称	类型	默认值	说明
id_or_num	str
int	None	要获取的标签页 id 或序号，序号从1开始，可传入负数获取倒数第几个，不是视觉排列顺序，而是激活顺序
title	str	None	要匹配 title 的文本，模糊匹配，为None则匹配所有
url	str	None	要匹配 url 的文本，模糊匹配，为None则匹配所有
tab_type	str
list
tuple	'page'	标签页类型，可用列表输入多个，如'page'、'iframe'等，为None则匹配所有
as_id	bool	False	是否返回标签页 id 而不是标签页对象
返回类型	说明
MixTab	as_id为False时返回获取到的标签页对象
str	as_id为True时返回获取到的标签页的 id
from DrissionPage import Chromium

browser = Chromium()
tab = browser.get_tab()

📌 get_tabs()​
此方法用于获取多个符合条件的MixTab对象或它们的 id组成的列表。
参数名称	类型	默认值	说明
title	str	None	要匹配 title 的文本，模糊匹配，为None则匹配所有
url	str	None	要匹配 url 的文本，模糊匹配，为None则匹配所有
tab_type	str
list
tuple	'page'	标签页类型，可用列表输入多个，如'page'、'iframe'等，为None则匹配所有
as_id	bool	False	是否返回标签页 id 而不是标签页对象
返回类型	说明
List[MixTab]	as_id为False时返回获取到的标签页对象组成的列表
List[str]	as_id为True时返回获取到的标签页的 id 组成的列表
📌 latest_tab​
此属性返回最新的标签页对象或 id，最新标签页指最后创建或最后被激活的。
如果关闭单例模式，即当Settings.singleton_tab_obj为False时，返回标签页的 id。
返回类型	说明
MixTab	单例模式时返回标签页对象
str	非单例模式时返回标签页 id
📌 tabs_count​
此属性返回标签页数量，只统计普通标签页（即'page'、'webview'类型）。
类型：int
📌 tab_ids​
此属性返回所有标签页 id 组成的列表，只统计普通标签页（即'page'、'webview'类型）。
类型：List[str]
✅️️ 标签页操作​
📌 new_tab()​
此方法用于新建标签页，并返回标签页对象。
参数名称	类型	默认值	说明
url	str	None	新标签页跳转到的网址，为None时新建空标签页
new_window	bool	False	是否在新窗口打开标签页，隐身模式下无效
background	bool	False	是否不激活新标签页，隐身模式和访客模式及new_window为True时无效
new_context	bool	False	是否创建独立环境，隐身模式和访客模式下无效
返回类型	说明
MixTab	标签页对象
📌 activate_tab()​
此方法用于使一个标签页显示到前端。可传入 Tab 对象、标签页 id、标签页序号。
注意标签页序号不是视觉顺序，而是激活顺序。
说明
标签页没有焦点的概念，多个标签页可以并行操作，这个方法不会对所谓焦点产生什么影响。
参数名称	类型	默认值	说明
id_ind_tab	str
int
ChromiumTab
MixTab	必填	标签页 id（str）、Tab 对象或标签页序号（int），序号从1开始
返回：None
📌 close_tabs()​
此方法用于关闭标签页。可指定多个，可关闭指定标签页以外的。
参数名称	类型	默认值	说明
tabs_or_ids	str
ChromiumTab
MixTab
List|Tuple[ChromiumTab|MixTab|str]	必填	指定的标签页对象或 id，可用列表或元组传入多个
others	bool	False	是否关闭指定标签页之外的
返回：None
📌 单例模式说明​
默认设置下，一个标签页只有一个 Tab 对象。
对同一个标签页反复使用get_tab()获取到的是同一个对象。
如上文所述，latest_tab获取的也是曾经生成过的 Tab 对象。
如果需要多个 Tab 对象共同管理一个标签页，可关闭单例模式：
from DrissionPage.common import Settings

Settings.set_singleton_tab_obj(False)

关闭后，每次get_tab()都会创建新的 Tab 对象，latest_tab改成返回 Tab 对象的 id。
from DrissionPage import Chromium
from DrissionPage.common import Settings

Settings.set_singleton_tab_obj(False)
browser = Chromium()
tab1 = browser.get_tab()
tab2 = browser.get_tab()
print(tab1.title, id(tab1))
print(tab2.title, id(tab2))

输出：
新标签页 2500121968848
新标签页 2500125672272

✅️️ 浏览器运行参数​
浏览器运行设置是一些总体的运行参数。
新标签页对象会继承浏览器的运行设置，但标签页对象后再修改浏览器设置，已生成的设置也不会改变。
设置优先级：Tab 对象设置 > Chromium对象设置 > Settings设置
📌 user_data_path​
此参数返回浏览器返回用户文件夹路径。
类型：str
📌 download_path​
此参数返回浏览器返回默认下载路径。
类型：str
📌 几种超时参数​
此参数返回所有超时设置，单位为秒，有base、page_load、script三种。
timeouts.base：各种等待的基础超时设置
timeouts.page_load：页面文档加载的超时设置
timeouts.script：JavaScript 运行超时设置
类型：float
📌 timeout​
此参数返回基础超时设置，单位为秒，即timeouts.base。
类型：float
📌 load_mode​
此参数返回页面加载模式，包括'none'、'normal'、'eager'三种。
类型：str
✅️️ 浏览器运行设置​
📌 set.timeouts()​
此方法用于设置运行时的各种超时时间，单位为秒。
参数名称	类型	默认值	说明
base	float	None	各种等待的默认超时时间，为None则不修改
page_load	float	None	页面文档加载超时时间，为None则不修改
script	float	None	脚本运行超时时间，为None则不修改
返回：None
📌 加载模式设置​
此方法用于设置页面加载模式。具体使用方法详见访问网页章节。
set.load_mode.normal()：等待所有资源加载完毕的模式
set.load_mode.eager()：等待文档加载完即停止加载的模式
set.load_mode.none()：不会主动停止加载的模式
返回：None
📌 set.retry_times()​
此方法用于设置页面连接失败重连次数。
参数名称	类型	默认值	说明
times	int	必填	重连次数
返回：None
📌 set.retry_interval()​
此方法用于设置连接失败重连间隔（秒）。
参数名称	类型	默认值	说明
interval	float	必填	重连间隔
返回：None
📌 set.cookies()​
此方法用于设置一个或多个 cookie。
注意
用这个方法设置 cookies 记得带上domain属性。
参数名称	类型	默认值	说明
cookies	CookieJar
Cookie
list
tuple
str
dict	必填	支持多种格式的 cookies 信息，一个或多个都可以
返回：None
📌 set.cookies.clear()​
此方法用于清除浏览器所有 cookies。
参数： 无
返回：None
📌 set.auto_handle_alert()​
此方法用于设置是否启用自动处理 alert 弹窗。
参数名称	类型	默认值	说明
on_off	bool	True	bool表示开或关，传入None表示使用Settings设置
accept	bool	True	处理 alert 的方式，确定还是取消
返回：None
📌 set.download_path()​
此方法用于设置下载文件默认保存路径。
参数名称	类型	默认值	说明
path	Path
str
None	必填	文件夹路径，传入None表示当前文件夹
返回：None
📌 set.download_file_name()​
此方法用于设置下一个被下载文件的名称。
有些下载是从临时闪现的标签页触发的，这种需要由浏览器对象去捕捉和设置下载信息。
参数名称	类型	默认值	说明
name	str	None	文件名，可不含后缀，会自动使用远程文件后缀，为None使用远程文件名
suffix	str	None	后缀名，显式设置后缀名，不使用远程文件后缀
返回：None
📌 set.when_download_file_exists()​
此方法用于设置当存在同名文件时的处理方式。
参数名称	类型	默认值	说明
mode	str	必填	可在'rename'、'overwrite'、'skip'、'r'、'o'、's'中选择
'rename'或'r'：自动重命名，在文件名后加序号，如'_1'
'overwrit'或'o'：覆盖已有文件
'skip'或's'：跳过，不下载
返回：None
📌 set.NoneElement_value()​
此方法用于设置查找元素失败时返回的空元素是否返回设定值。详见元素查找行为章节。
参数名称	类型	默认值	说明
value	Any	None	设置的设定值
on_off	bool	True	是否启用
返回：None
✅️️ 浏览器信息​
📌 cookies()​
此方法以列表形式返回浏览器所有域名的 cookies，cookie 是dict格式。
参数名称	类型	默认值	说明
all_info	bool	False	是否返回所有内容，False则只返回'name'、'value'、'domain'三个属性
返回：CookiesList
除列表格式，还能以其它格式返回：
cookies().as_str：以str格式返回，只包含name和value字段，'name1=value1; name2=value2'格式
cookies().as_dict：以dict格式返回，只包含name和value字段，{'name1': 'value1', 'name2': 'value1'}格式
cookies().as_json：把列表转换为 json 返回
📌 process_id​
此属性返回浏览器进程 pid。
类型：int
📌 states.is_alive​
此属性返回浏览器是否仍可用。
类型：bool
📌 states.is_existed​
此属性返回浏览器是否接管的，而非本程序创建的。
类型：bool
📌 states.is_headless​
此属性返回浏览器是否无头模式。
类型：bool
📌 states.is_incognito​
此属性返回浏览器是否无痕模式。
类型：bool
✅️️ 其它浏览器行为​
📌 wait()​
此方法用于等待若干秒。
scope为None时，效果与time.sleep()没有区别，等待指定秒数。
scope不为None时，获取两个参数之间的一个随机值，等待这个数值的秒数。
参数名称	类型	默认值	说明
second	float	必填	要等待的秒数，scope不为None时表示随机数范围起始值
scope	float	None	随机数范围结束值
返回类型	说明
Chromium	浏览器对象自身
📌 wait.new_tab()​
此方法用于等待新标签页出现。
参数名称	类型	默认值	说明
timeout	float	None	超时时间（秒），为None则使用对象timeout属性
curr_tab	str
ChromiumTab
MixTab	None	指定当前最新的 Tab 对象或标签页 id，用于判断新标签页出现，为None自动获取
raise_err	bool	None	等待失败时是否报错，为None时根据Settings设置
返回类型	说明
str	等待成功返回新标签页 id
False	等待失败返回False
📌 wait.download_begin()​
此方法用于等待浏览器下载开始。
有些下载是从临时闪现的标签页触发的，这种需要由浏览器对象去捕捉。
参数名称	类型	默认值	说明
timeout	float	None	超时时间（秒），None使用页面对象超时时间
cancel_it	bool	False	是否取消该任务
返回类型	说明
DownloadMission	等待成功返回下载任务对象
False	等待失败返回False
📌 wait.downloads_done()​
此方法用于等待所有浏览器下载任务结束。
参数名称	类型	默认值	说明
timeout	float	None	超时时间（秒），为None时无限等待
cancel_if_timeout	bool	True	超时时是否取消剩余任务
返回类型	说明
bool	是否等待成功
📌 clear_cache()​
此方法用于清除缓存。
参数名称	类型	默认值	说明
cache	bool	True	是否清除缓存
cookies	bool	True	是否清除 cookies
返回：None
📌 reconnect()​
此方法用于关闭与浏览器连接，并重新创建连接。
参数： 无
返回：None
📌 quit()​
此方法用于关闭浏览器。
参数名称	类型	默认值	说明
timeout	float	5	等待浏览器关闭超时时间（秒）
force	bool	False	是否立刻强制终止进程
del_data	bool	False	是否删除用户文件夹
返回：None