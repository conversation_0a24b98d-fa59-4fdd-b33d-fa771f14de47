# -*- coding: utf-8 -*-
"""
验证码机制深度研究脚本
分析海关查询系统的验证码生成、验证和绕过方法
"""

import requests
import time
import json
import base64
from urllib.parse import parse_qs, urlparse
import ddddocr
from DrissionPage import Chromium, ChromiumOptions
import os

class CaptchaResearcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 初始化OCR
        self.ocr = ddddocr.DdddOcr()
        
        # 存储分析结果
        self.captcha_analysis = {
            'captcha_urls': [],
            'session_cookies': [],
            'form_submissions': [],
            'api_patterns': []
        }
    
    def analyze_page_structure(self, url):
        """分析页面结构和表单"""
        print(f"=== 分析页面结构: {url} ===")
        
        response = self.session.get(url)
        print(f"页面状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"Cookies: {dict(response.cookies)}")
        
        # 保存页面源码用于分析
        with open(f'page_source_{url.split("/")[-1]}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        # 分析表单结构
        self.analyze_form_structure(response.text, url)
        
        return response
    
    def analyze_form_structure(self, html_content, url):
        """分析表单结构"""
        print("\n--- 分析表单结构 ---")
        
        # 查找表单相关的关键信息
        import re
        
        # 查找ViewState等ASP.NET特有字段
        viewstate_pattern = r'name="__VIEWSTATE".*?value="([^"]*)"'
        viewstate_match = re.search(viewstate_pattern, html_content, re.IGNORECASE)
        if viewstate_match:
            print(f"发现ViewState: {viewstate_match.group(1)[:50]}...")
        
        # 查找EventValidation
        eventvalidation_pattern = r'name="__EVENTVALIDATION".*?value="([^"]*)"'
        eventvalidation_match = re.search(eventvalidation_pattern, html_content, re.IGNORECASE)
        if eventvalidation_match:
            print(f"发现EventValidation: {eventvalidation_match.group(1)[:50]}...")
        
        # 查找验证码相关元素
        captcha_patterns = [
            r'id="([^"]*[Ii]mage[^"]*)"',
            r'id="([^"]*[Cc]ode[^"]*)"',
            r'src="([^"]*[Ii]mage[^"]*\.aspx[^"]*)"'
        ]
        
        for pattern in captcha_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                print(f"发现验证码相关元素: {matches}")
    
    def test_captcha_generation(self, base_url):
        """测试验证码生成机制"""
        print("\n=== 测试验证码生成机制 ===")
        
        # 测试不同的验证码URL
        captcha_urls = [
            f"{base_url}/Image.aspx",
            f"{base_url}/ValidateCode.aspx",
            f"{base_url}/VerifyCode.aspx",
            f"{base_url}/Captcha.aspx"
        ]
        
        for captcha_url in captcha_urls:
            try:
                print(f"\n测试验证码URL: {captcha_url}")
                response = self.session.get(captcha_url)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
                    print(f"Content-Length: {len(response.content)}")
                    
                    # 如果是图片，保存并识别
                    if 'image' in response.headers.get('Content-Type', ''):
                        filename = f"captcha_{int(time.time())}.png"
                        with open(filename, 'wb') as f:
                            f.write(response.content)
                        
                        # OCR识别
                        try:
                            result = self.ocr.classification(response.content)
                            print(f"OCR识别结果: {result}")
                            
                            self.captcha_analysis['captcha_urls'].append({
                                'url': captcha_url,
                                'status': response.status_code,
                                'content_type': response.headers.get('Content-Type'),
                                'size': len(response.content),
                                'ocr_result': result,
                                'cookies': dict(response.cookies),
                                'filename': filename
                            })
                        except Exception as e:
                            print(f"OCR识别失败: {e}")
                
            except Exception as e:
                print(f"请求失败: {e}")
    
    def test_session_management(self, base_url):
        """测试会话管理机制"""
        print("\n=== 测试会话管理机制 ===")
        
        # 测试多次请求验证码，观察Cookie变化
        captcha_url = f"{base_url}/Image.aspx"
        
        for i in range(5):
            print(f"\n第{i+1}次请求验证码:")
            response = self.session.get(captcha_url)
            
            if response.status_code == 200:
                cookies = dict(response.cookies)
                print(f"Cookies: {cookies}")
                
                # 分析Cookie变化
                self.captcha_analysis['session_cookies'].append({
                    'request_num': i+1,
                    'cookies': cookies,
                    'timestamp': time.time()
                })
            
            time.sleep(1)
    
    def test_form_submission(self, page_url, test_data):
        """测试表单提交机制"""
        print("\n=== 测试表单提交机制 ===")
        
        # 先获取页面获取必要的表单字段
        response = self.session.get(page_url)
        html_content = response.text
        
        # 提取ASP.NET表单字段
        import re
        
        viewstate_match = re.search(r'name="__VIEWSTATE".*?value="([^"]*)"', html_content, re.IGNORECASE)
        eventvalidation_match = re.search(r'name="__EVENTVALIDATION".*?value="([^"]*)"', html_content, re.IGNORECASE)
        
        if not viewstate_match:
            print("未找到ViewState，可能不是ASP.NET页面")
            return
        
        # 获取验证码
        base_url = '/'.join(page_url.split('/')[:-1])
        captcha_response = self.session.get(f"{base_url}/Image.aspx")
        
        if captcha_response.status_code == 200:
            captcha_code = self.ocr.classification(captcha_response.content)
            print(f"验证码识别结果: {captcha_code}")
        else:
            captcha_code = "1234"  # 测试用
        
        # 构造表单数据
        form_data = {
            '__VIEWSTATE': viewstate_match.group(1),
            '__EVENTVALIDATION': eventvalidation_match.group(1) if eventvalidation_match else '',
            test_data['manifest_field']: test_data['manifest_value'],
            test_data['code_field']: captcha_code,
            test_data['submit_field']: test_data['submit_value']
        }
        
        print(f"提交表单数据: {form_data}")
        
        # 提交表单
        response = self.session.post(page_url, data=form_data)
        print(f"提交结果状态码: {response.status_code}")
        
        # 分析响应
        if '验证码' in response.text:
            print("响应中包含验证码相关信息")
        if '查询记录' in response.text:
            print("响应中包含查询结果信息")
        
        # 保存响应用于分析
        with open(f'form_response_{int(time.time())}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        self.captcha_analysis['form_submissions'].append({
            'form_data': form_data,
            'response_status': response.status_code,
            'response_length': len(response.text),
            'captcha_code': captcha_code
        })
    
    def analyze_api_patterns(self):
        """分析API调用模式"""
        print("\n=== 分析API调用模式 ===")
        
        # 基于收集的数据分析模式
        print("验证码URL模式:")
        for captcha_info in self.captcha_analysis['captcha_urls']:
            if captcha_info['status'] == 200:
                print(f"  - {captcha_info['url']}: {captcha_info['ocr_result']}")
        
        print("\nCookie模式:")
        if len(self.captcha_analysis['session_cookies']) > 1:
            first_cookies = self.captcha_analysis['session_cookies'][0]['cookies']
            last_cookies = self.captcha_analysis['session_cookies'][-1]['cookies']
            
            print(f"  首次请求Cookies: {first_cookies}")
            print(f"  最后请求Cookies: {last_cookies}")
            
            # 分析Cookie变化
            changed_cookies = {}
            for key in first_cookies:
                if key in last_cookies and first_cookies[key] != last_cookies[key]:
                    changed_cookies[key] = {
                        'first': first_cookies[key],
                        'last': last_cookies[key]
                    }
            
            if changed_cookies:
                print(f"  变化的Cookies: {changed_cookies}")
            else:
                print("  Cookies未发生变化")
    
    def save_analysis_results(self):
        """保存分析结果"""
        with open('captcha_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.captcha_analysis, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析结果已保存到 captcha_analysis_results.json")

def main():
    researcher = CaptchaResearcher()
    
    # 分析两个查询页面
    pages = [
        {
            'url': 'http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx',
            'name': '公路舱单查询',
            'test_data': {
                'manifest_field': 'MRoadTransportQueryCtrl1$txtManifestID',
                'manifest_value': 'TEST123456',
                'code_field': 'MRoadTransportQueryCtrl1$txtCode',
                'submit_field': 'MRoadTransportQueryCtrl1$btQuery',
                'submit_value': '查询'
            }
        },
        {
            'url': 'http://query.customs.gov.cn/MNFTQ/MRoadQuery.aspx',
            'name': '提运单信息查询',
            'test_data': {
                'manifest_field': 'MRoadQueryCtrl1$txtManifestID',
                'manifest_value': 'TEST123456',
                'code_field': 'MRoadQueryCtrl1$txtCode',
                'submit_field': 'MRoadQueryCtrl1$btQuery',
                'submit_value': '查询'
            }
        }
    ]
    
    for page_info in pages:
        print(f"\n{'='*50}")
        print(f"分析页面: {page_info['name']}")
        print(f"{'='*50}")
        
        # 分析页面结构
        researcher.analyze_page_structure(page_info['url'])
        
        # 测试验证码生成
        base_url = '/'.join(page_info['url'].split('/')[:-1])
        researcher.test_captcha_generation(base_url)
        
        # 测试会话管理
        researcher.test_session_management(base_url)
        
        # 测试表单提交
        researcher.test_form_submission(page_info['url'], page_info['test_data'])
        
        time.sleep(2)  # 避免请求过快
    
    # 分析API模式
    researcher.analyze_api_patterns()
    
    # 保存结果
    researcher.save_analysis_results()

if __name__ == "__main__":
    main()
