# %%
from DrissionPage import Chromium, ChromiumOptions
import pandas as pd
import ddddocr
import shutil
import json
import time
# -*- coding: utf-8 -*-
# @Software: PyCharm
import requests
import time
import json
from PIL import Image
from io import BytesIO
from collections import Counter
import os
import random




# 先判断文件夹是否存在，如果不存在则创建它
if not os.path.exists('数据落地'):
    os.mkdir('数据落地')


dddocr = ddddocr.DdddOcr()


# %%
# 读取excel数据
# 读取当前路径的excel文件
import os
# 获取当前目录
current_directory = os.getcwd()
# 获取当前目录下所有的 CSV 文件
csv_files = [f for f in os.listdir(current_directory) if f.endswith('.csv')]
# 检查是否有 CSV 文件
if csv_files:
    # 取第一个 CSV 文件
    first_csv_file = csv_files[0]
    file_path = os.path.join(current_directory, first_csv_file)
    df = pd.read_csv(file_path)
    #print(df.info())

# df = pd.read_csv('202412切割output_1.csv')
    # 读取第一列的数据
    data = df.iloc[:, 0].tolist()
    print(data[0],len(data))

#tab = Chromium().latest_tab


# %%


co = ChromiumOptions().auto_port()

tab = Chromium(addr_or_opts=co).latest_tab


#tab2 = Chromium(addr_or_opts=co).latest_tab

#tab2.get('https://DrissionPage.cn')
#tab.get('https://www.baidu.com')


# %%
# 图片元素定位
    # 定位元素
def img_is_load_true():
    element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1')

    if element:
        # 执行 JavaScript 检查图片是否加载完成
        is_loaded = tab.run_js('return arguments[0].complete && (arguments[0].naturalWidth !== 0);', element)
        if is_loaded:
            print("图片已成功加载。")
            return True
        else:
            print("图片加载失败。")
            return False

    else:
        print("未找到指定元素。")
        return False




# %%
def pass_yzm(i):
    tab.get('http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx')

    while not img_is_load_true():
        tab.refresh()
        time.sleep(0.5)

    input_element = tab.ele('x://*[@id="MRoadTransportQueryCtrl1_txtManifestID"]') # document.querySelector("#ID_codeName")
    input_element.input(i)

        


    # 定位图片元素
    element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1')

    element.get_screenshot()
    bytes_str = element.get_screenshot(as_bytes='png')
    result = dddocr.classification(bytes_str)

    print('是四个验证码')
    input_element = tab.ele('x://*[@id="MRoadTransportQueryCtrl1_txtCode"]')
    input_element.clear()

    input_element.input(result)
    #点击按钮
    input_element = tab.ele('x://*[@id="MRoadTransportQueryCtrl1_btQuery"]')
    input_element.click()


# %%
def ok_yzm():
        # 使用 CSS 选择器定位元素
            # 使用 XPath 定位元素，XPath 可以通过 CSS 选择器转换得到
        time.sleep(0.5)

        element = tab.ele('@id=MRoadTransportQueryCtrl1_Image1',timeout=2)
        if element:
            print("成功定位到元素。")
            
            # 可以在这里对元素进行其他操作，例如点击、获取属性等
            # element.click()  # 点击元素
            # src = element.attr('src')  # 获取元素的 src 属性
            # print(f"元素的 src 属性值为: {src}")
        else:
            print("未找到指定元素。")


        element = tab.ele('@id=MRoadTransportQueryCtrl1_dgView')

        # 执行 JavaScript 脚本提取指定 tr 元素下的 td 内容
        js_code = """
        const tr = document.querySelector("#MRoadTransportQueryCtrl1_dgView > tbody > tr:nth-child(2)");
        if (tr) {
            const tds = tr.querySelectorAll('td');
            const tdContents = [];
            tds.forEach(td => {
                const a = td.querySelector('a');
                if (a) {
                    tdContents.push(a.textContent.trim());
                } else {
                    tdContents.push(td.textContent.trim());
                }
            });
            return tdContents;
        }
        return null;
        """
        td_contents = tab.run_js(js_code)

        if td_contents:
            print("提取到的 td 内容：")
            print(td_contents)
            joined_list = '\t'.join(map(str, td_contents))

            with open(file_path, 'a', encoding='utf-8') as file:
                # 遍历列表中的每个元素
                file.write(joined_list)
                # 写入换行符
                file.write('\n')

            # for content in td_contents:
            #     print(content)
        else:
            print("未找到指定的 tr 元素或未提取到 td 内容。")

# %%
file_path = "output.txt"





for i in data:
    while True:
        pass_yzm(i)
        tab.wait.doc_loaded(30)
        time.sleep(0.5)
        if (tab.ele('x://*[@id="MRoadTransportQueryCtrl1_tdMessage"]').text =='根据条件没有找到相关的查询记录！'):
            print('没有找到相关的查询记录！')
            break

        if (tab.ele('x://*[@id="MRoadTransportQueryCtrl1_tdMessage"]').text !='验证码填写不正确!'):
            ok_yzm()
            break


# %%

# 定位元素
element = tab.ele('@id=MRoadTransportQueryCtrl1_tdMessage')

if element:
    # 执行 JavaScript 代码判断元素是否可见
    is_visible_js = '''
    var elem = arguments[0];
    return (elem.offsetWidth > 0 || elem.offsetHeight > 0) && getComputedStyle(elem).visibility!== 'hidden';
    '''
    is_visible = tab.run_js(is_visible_js, element)

    if is_visible:
        print("元素可见。")
    else:
        print("元素不可见。")
else:
    print("未找到指定元素。")


