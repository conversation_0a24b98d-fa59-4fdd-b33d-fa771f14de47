import os
import cx_Oracle
import datetime

# 数据库连接信息
DB_USER = "manifest_dcb"
DB_PASSWORD = "manifest_dcb"
DB_HOST = "*************"
DB_SERVICE = "TEST"

# 文件路径
CYQB_FILE = "base承运确保/承运确报信息all.txt"  # 承运确保文件路径
TYDXX_FILE = "base提运单信息/base提运单信息.txt"  # 提运单信息文件路径

def connect_to_db():
    """连接到Oracle数据库"""
    try:
        connection_string = f"{DB_USER}/{DB_PASSWORD}@{DB_HOST}/{DB_SERVICE}"
        connection = cx_Oracle.connect(connection_string)
        print("成功连接到Oracle数据库")
        return connection
    except cx_Oracle.Error as error:
        print(f"连接数据库时出错: {error}")
        return None

def truncate_tables(connection):
    """清空表数据"""
    cursor = connection.cursor()
    try:
        cursor.execute("TRUNCATE TABLE temp_cyqb")
        cursor.execute("TRUNCATE TABLE temp_tydxx")
        print("已清空表数据")
    except cx_Oracle.Error as error:
        print(f"清空表时出错: {error}")
    finally:
        cursor.close()

def import_cyqb_data(connection):
    """导入承运确保数据"""
    if not os.path.exists(CYQB_FILE):
        print(f"文件不存在: {CYQB_FILE}")
        return
    
    cursor = connection.cursor()
    insert_sql = """
    INSERT INTO temp_cyqb (
        SELECTION, BATCH_NUMBER, VEHICLE_NUMBER, CUSTOMS_CODE, 
        REPORT_TYPE, TRANSMITTER_CODE, TRANSMITTER_NAME, 
        VEHICLE_NAME, DRIVER_CODE, DRIVER_NAME, VERIFICATION_STATUS, 
        CREATE_TIME
    ) VALUES (
        :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, SYSDATE
    )
    """
    
    try:
        count = 0
        with open(CYQB_FILE, 'r', encoding='utf-8') as file:
            for line in file:
                if not line.strip():
                    continue
                
                fields = line.strip().split('\t')
                if len(fields) >= 11:  # 确保有足够的字段
                    # 准备数据
                    data = fields[:11]  # 取前11个字段
                    
                    # 执行插入
                    cursor.execute(insert_sql, data)
                    count += 1
                    
                    # 每1000条提交一次
                    if count % 1000 == 0:
                        connection.commit()
                        print(f"已导入 {count} 条承运确保数据")
        
        # 最后提交剩余的数据
        connection.commit()
        print(f"承运确保数据导入完成，共导入 {count} 条记录")
    
    except Exception as e:
        print(f"导入承运确保数据时出错: {e}")
        connection.rollback()
    finally:
        cursor.close()

def import_tydxx_data(connection):
    """导入提运单信息数据"""
    if not os.path.exists(TYDXX_FILE):
        print(f"文件不存在: {TYDXX_FILE}")
        return
    
    cursor = connection.cursor()
    insert_sql = """
    INSERT INTO temp_tydxx (
        BATCH_NUMBER, BILL_NUMBER, IMPORT_EXPORT, CUSTOMS_CODE, 
        REPORT_STATUS, REPORT_TIME, VERIFICATION_STATUS, WRITE_OFF_STATUS, 
        TALLY_STATUS, ARRIVAL_STATUS, RELEASE_STATUS, CLOSING_STATUS,
        CREATE_TIME
    ) VALUES (
        :1, :2, :3, :4, :5, TO_DATE(:6, 'YYYY/MM/DD HH24:MI:SS'), 
        :7, :8, :9, :10, :11, :12, SYSDATE
    )
    """
    
    try:
        count = 0
        with open(TYDXX_FILE, 'r', encoding='utf-8') as file:
            for line in file:
                if not line.strip():
                    continue
                
                fields = line.strip().split('\t')
                if len(fields) >= 12:  # 确保有足够的字段
                    # 执行插入
                    cursor.execute(insert_sql, fields[:12])
                    count += 1
                    
                    # 每1000条提交一次
                    if count % 1000 == 0:
                        connection.commit()
                        print(f"已导入 {count} 条提运单信息数据")
        
        # 最后提交剩余的数据
        connection.commit()
        print(f"提运单信息数据导入完成，共导入 {count} 条记录")
    
    except Exception as e:
        print(f"导入提运单信息数据时出错: {e}")
        connection.rollback()
    finally:
        cursor.close()

def main():
    """主函数"""
    # 连接数据库
    connection = connect_to_db()
    if not connection:
        return
    
    try:
        # 清空表
        #truncate_tables(connection)
        
        # 导入承运确保数据
        import_cyqb_data(connection)
        
        # 导入提运单信息数据
        import_tydxx_data(connection)
        
        print("数据导入完成")
    
    except Exception as e:
        print(f"执行过程中出错: {e}")
    finally:
        connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main() 