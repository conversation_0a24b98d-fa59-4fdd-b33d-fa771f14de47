

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

<head><title>



</title>

    <style id="StylePlaceholder" type="text/css"></style>

    <link id="APortals__default_" href="css/default.css" rel="stylesheet" type="text/css" /><link id="APortals_0_Skins_osc_" href="css/skin.css" rel="stylesheet" type="text/css" /><link id="APortals_0_" href="css/portal.css" rel="stylesheet" type="text/css" />

    <script src="css/jquery.min.js" type="text/javascript"></script>

    <link href="css/TabStrip.Default.css" rel="stylesheet" type="text/css" /><link href="css/RibbonBar.Default.css" rel="stylesheet" type="text/css" /><link href="css/TabStrip.Default(1).css" rel="stylesheet" type="text/css" /></head>

<body topmargin="0" rightmargin="0" bottommargin="0" leftmargin="0">

    <form name="form1" method="post" action="MRoadQuery.aspx" id="form1">

<div>

<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwUKMTg2Mjg1MDcxNA9kFgICAw9kFgICAQ9kFgYCBQ8WAh4JaW5uZXJodG1sBTDmoLnmja7mnaHku7bmsqHmnInmib7liLDnm7jlhbPnmoTmn6Xor6LorrDlvZXvvIFkAgsPDxYCHgdFbmFibGVkZ2RkAg0PPCsACwEADxYIHghEYXRhS2V5cxYAHgtfIUl0ZW1Db3VudGYeCVBhZ2VDb3VudAIBHhVfIURhdGFTb3VyY2VJdGVtQ291bnRmZGRk5HNYQbQuRVltCaqhYYZI/gAAAAA=" />

</div>



<div>



	<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEWBQKz6/KsBgKIg6eMDAKZl6i5AwLPjaW3BAK63by2BIVI1Zz5SbTlw2eUUX1INJ4AAAAA" />

</div>



    <SCRIPT src="css/esscore.js" 

type=text/javascript></SCRIPT>



<SCRIPT src="css/MicrosoftAjax.js" 

type=text/javascript></SCRIPT>

 <INPUT name=ScrollTop id=ScrollTop type=hidden> <INPUT name=__essVariable 

id=__essVariable type=hidden>

<SCRIPT src="css/jquery-1.6.4.js" 

type=text/javascript></SCRIPT>



<SCRIPT src="css/navjs.js" 

type=text/javascript></SCRIPT>



<SCRIPT typa="text/javascript">

<!--

    function setTab(name, cursel, n) {

        for (i = 1; i <= n; i++) {

            var menu = document.getElementById(name + i);

            var con = document.getElementById("ess_con_" + name + "_" + i);

            menu.className = i == cursel ? "hover" : "";

            con.style.display = i == cursel ? "block" : "none";

        }

    }

    var test = {};

    test["NTit01"] = "navTitle01";

    test["NTit02"] = "navTitle02";

    test["NTit03"] = "navTitle03";

    test["NTit04"] = "navTitle04";

    test["NTit05"] = "navTitle05";

    //-->

    function showNav(nav) {

        var navId = nav.id;

        nav.parentNode.className = test[navId];

        var cont = document.getElementById(navId + "_con");

        var parent = cont.parentNode.children;

        for (var i = 0, n = parent.length; i < n; i++) {

            var di = "";

            if (cont != parent[i]) {

                di = "none";

            }

            parent[i].style.display = di;

        }

    }

</SCRIPT>

 

<DIV class=yqTop>

<DIV class=topIn>

<DIV class="topLef fl" id=ess_mod1>

<SCRIPT language=javascript>

    today = new Date(); function initArray() { this.length = initArray.arguments.length; for (var i = 0; i < this.length; i++) this[i + 1] = initArray.arguments[i] }; var d = new initArray("星期日",

"星期一", "星期二", "星期三", "星期四", "星期五", "星期六"); document.write("<font class=ttt>", today.getFullYear(), "年", today.getMonth() + 1, "月",

today.getDate(), "日 ", d[today.getDay() + 1], "</font>");

</SCRIPT>

</DIV>

<DIV class="topRig fr" id=ess_mod2>

<DIV class=topLinks><A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">登录</A> <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">注册</A>| <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">简体</A> <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">繁体</A> <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">English</A>| <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">海关电邮</A>| <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">使用帮助</A>| <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">订阅服务</A>| <A 

href="http://www.customs.gov.cn/publish/portal0/tab9406/#">手机版</A>| <SPAN 

id=ewm><A id=ewm href="http://www.customs.gov.cn/publish/portal0/tab9406/#" 

target=_blank>二维码</A> 

<DIV class=erweima style="DISPLAY: none"><A href="javascript:viod(0);"><IMG 

width=112 height=148 alt=二维码 src="css/erweima_03.jpg"> 

</A></DIV></SPAN></DIV>

<STYLE type=text/css>.topLinks {

	POSITION: relative; TEXT-ALIGN: right

}

.topLinks A {

	CURSOR: pointer; COLOR: #8e8e8e; PADDING-BOTTOM: 0px; PADDING-TOP: 0px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px

}

.topLinks A:hover {

	TEXT-DECORATION: none; COLOR: #8e8e8e

}

.erweima {

	HEIGHT: 148px !important; WIDTH: 122px !important; RIGHT: -5px; POSITION: absolute; Z-INDEX: 10000; TOP: 29px

}

</STYLE>



<SCRIPT type=text/javascript>

    jQuery(function () {

        jQuery("#ewm:has(div)").hover(function () {

            jQuery(this).children("div").stop(true, true).slideDown(400);

        }, function () {

            jQuery(this).children("div").stop(true, true).slideUp("fast");

        });

    })

</SCRIPT>



</DIV></DIV></DIV>

<DIV class="wrapper1  clear">

<DIV class=wrap2><!--head start-->

<DIV class=header>

<DIV class="logo fl" id=ess_mod3><A 

style="HEIGHT: 63px; WIDTH: 386px; DISPLAY: block" 

href="http://www.customs.gov.cn/tabid/49564/Default.aspx"><img src="css/yqTopbj.jpg"/></A></DIV>

<DIV class="headRig fr">

<DIV class="fl topBox ESSEmptyPane" id=ess_mod4></DIV>

<DIV class="tel fr" id=ess_mod04><A style="HEIGHT: 50px; DISPLAY: block" 

href="http://www.customs.gov.cn/tabid/49590/Default.aspx"></A></DIV></DIV></DIV><!--head end--><!--main start-->

<DIV class=con_box1>

<DIV class=tonglandx1 id=ess_ContentPane>

<DIV style="MARGIN: 13px"><SPAN style="COLOR: #000000">当前位置 ：</SPAN>

    <SPAN 

id=ess_essBREADCRUMB_lblBreadCrumb>

<SPAN id=SPAN1><A class=SkinObject 

href="http://www.customs.gov.cn/publish/portal0/tab9406">舱单信息查询</A></SPAN>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

    <A class=SkinObject 

href="MQuery.aspx">海空舱单信息查询</A>&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;

    <A class=SkinObject 

href="MRoadQuery.aspx">公路舱单信息查询</A>&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;

    <a class="SkinObject" href="MRoadTransportQuery.aspx">公路舱单确报查询</a>

    </DIV>

<DIV class=ESSAlignleft id=ess_ctr30295_ContentPane><!-- Start_Module_30295 -->

<TABLE width="100%" class=ModESSHTMLC id=ess_ctr30295_ModuleContent border=0 

cellSpacing=0 cellPadding=0>

  <TBODY>

  <TR>

    <TD>

      <DIV class=Normal id=ess_ctr30295_HtmlModule_lblContent>

      <TABLE width="100%" border=0 cellSpacing=0 cellPadding=0 

      onload="resize1()">

        <TBODY>

        <TR>

          <TD align=center vAlign=top>

            <SCRIPT>

                function resize1() {

                    document.all.ifrm.height = ifrm.window.document.body.scrollHeight;

                }  

</SCRIPT>

            

<table width="97%" border="0" cellpadding="0" cellspacing="0" style="font-size: small">

    <tr>

        <td width="11%">

            货物运输批次号:</td>

        <td width="23%">

            <input name="MRoadQueryCtrl1$txtManifestID" type="text" value="TEST123456" id="MRoadQueryCtrl1_txtManifestID" style="width:90%;" />

        </td>

        <td width="11%">

            提(运)单号:</td>

        <td width="23%">

            <input name="MRoadQueryCtrl1$txtBillNo" type="text" id="MRoadQueryCtrl1_txtBillNo" style="width:90%;" />

        </td>

        <td width="11%">

            &nbsp;</td>

        <td width="23%">

            &nbsp;</td>

    </tr>

    <tr>

        <td id="MRoadQueryCtrl1_tdMessage" align="center" valign="middle" colspan="6" style="color: Red;

            font-weight: bold;" height="18px">根据条件没有找到相关的查询记录！</td>



    </tr>

    <tr>

    <td width="11%" colspan="2" style="color: #800080; font-weight: bold;">【公路舱单-提运单信息】</td>

    <td align="center" colspan="2" style="color: #800080; font-weight: bold;">&nbsp;</td><td></td><td align="right" width="23%" > <input name="MRoadQueryCtrl1$txtCode" type="text" value="679e" id="MRoadQueryCtrl1_txtCode" style="width:70px;margin-left: 0px" />

    <img id="MRoadQueryCtrl1_imgCode" src="Image.aspx" align="middle" style="border-width:0px;" />

    <input type="submit" name="MRoadQueryCtrl1$btQuery" value=" 查 询 " id="MRoadQueryCtrl1_btQuery" /></td>

    </tr>

</table>



<hr />

<div style="height: 622px;overflow:auto">

    <table cellspacing="0" cellpadding="4" border="0" id="MRoadQueryCtrl1_dgView" style="color:#333333;font-size:Small;width:100%;border-collapse:collapse;">

	<tr style="color:White;background-color:#5D7B9D;font-weight:bold;">

		<td align="left" style="font-weight:normal;font-style:normal;text-decoration:none;">货物运输批次号</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">提运单号</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">进出境标志</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">关区代码</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">确报状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">进出境日期</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">核注状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">核销状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">理货状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">运抵状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">放行状态</td><td align="center" style="font-weight:normal;font-style:normal;text-decoration:none;">结关状态</td>

	</tr>

</table>

</div>





            </TD></TR></TBODY></TABLE></DIV></TD></TR></TBODY></TABLE><!-- End_Module_30295 --></DIV></DIV></DIV></DIV></DIV></DIV>

<DIV class="yqFoot clear" id=ess_mod>

<DIV class=foot_out id=yqFoot>

<UL class="footLinks ">

  <LI><A id=aboutUs 

  href="http://www.customs.gov.cn/publish/portal0/tab9406/#">关于我们</A> | 

  <DIV class=showDiv 

  style="PADDING-TOP: 8px; DISPLAY: none; LINE-HEIGHT: 16px">中华人民共和国海关总署主办<BR>版权所有&nbsp;&nbsp;未经协议授权禁止转载&nbsp;&nbsp;京ICP备05066252号<BR>地址:北京市建国门内大街6号&nbsp;&nbsp;邮编:100730&nbsp;&nbsp;电话:010-65194114(总机) 

  </DIV></LI>

  <LI><A href="http://www.customs.gov.cn/tabid/49701/Default.aspx">网站留言</A> | 

  </LI>

  <LI><A 

  onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://test3.easysso.com');return(false);" 

  href="http://test3.easysso.com/">设为首页 </A>| </LI>

  <LI><A title=加入收藏 

  href="http://www.customs.gov.cn/publish/portal0/tab9406/#">加入收藏</A> | </LI>

  <LI><A id=aboutUs href="javascript:void(0);">相关链接</A> | 

  <DIV class=showDiv1 style="DISPLAY: none"><SELECT name=select 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>中国政府网</OPTION> <OPTION 

    value=http://www.gov.cn>中国政府网</OPTION></SELECT> <SELECT name=select1 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>国务院部门网站</OPTION> <OPTION 

    value=http://www.mfa.gov.cn>外交部</OPTION> <OPTION 

    value=http://www.ndrc.gov.cn>发展改革委</OPTION> <OPTION 

    value=http://www.moe.gov.cn>教育部</OPTION> <OPTION 

    value=http://www.most.gov.cn>科技部</OPTION> <OPTION 

    value=http://www.miit.gov.cn>工业和信息化部</OPTION> <OPTION 

    value=http://www.seac.gov.cn>国家民委</OPTION> <OPTION 

    value=http://www.mps.gov.cn>公安部</OPTION> <OPTION 

    value=http://www.mos.gov.cn>监察部</OPTION> <OPTION 

    value=http://www.mca.gov.cn>民政部</OPTION> <OPTION 

    value=http://www.moj.gov.cn>司法部</OPTION> <OPTION 

    value=http://www.mof.gov.cn>财政部</OPTION> <OPTION 

    value=http://www.mohrss.gov.cn>人力资源社会保障部</OPTION> <OPTION 

    value=http://www.mlr.gov.cn>国土资源部</OPTION> <OPTION 

    value=http://www.mep.gov.cn>环境保护部</OPTION> <OPTION 

    value=http://www.mohurd.gov.cn>住房城乡建设部</OPTION> <OPTION 

    value=http://www.moc.gov.cn>交通运输部</OPTION> <OPTION 

    value=http://www.china-mor.gov.cn>铁道部</OPTION> <OPTION 

    value=http://www.mwr.gov.cn>水利部</OPTION> <OPTION 

    value=http://www.moa.gov.cn>农业部</OPTION> <OPTION 

    value=http://www.mofcom.gov.cn>商务部</OPTION> <OPTION 

    value=http://www.mcprc.gov.cn>文化部</OPTION> <OPTION 

    value=http://www.moh.gov.cn>卫生部</OPTION> <OPTION 

    value=http://www.chinapop.gov.cn>人口计生委</OPTION> <OPTION 

    value=http://www.pbc.gov.cn>人民银行</OPTION> <OPTION 

    value=http://www.audit.gov.cn>审计署</OPTION> <OPTION 

    value=http://www.sasac.gov.cn>国资委</OPTION> <OPTION 

    value=http://www.customs.gov.cn>海关总署</OPTION> <OPTION 

    value=http://www.chinatax.gov.cn>税务总局</OPTION> <OPTION 

    value=http://www.saic.gov.cn>工商总局</OPTION> <OPTION 

    value=http://www.aqsiq.gov.cn>质检总局</OPTION> <OPTION 

    value=http://www.chinasarft.gov.cn>广电总局</OPTION> <OPTION 

    value=http://www.gapp.gov.cn>新闻出版总署(版权局)</OPTION> <OPTION 

    value=http://www.sport.gov.cn>体育总局</OPTION> <OPTION 

    value=http://www.chinasafety.gov.cn>安全监管总局</OPTION> <OPTION 

    value=http://www.stats.gov.cn>统计局</OPTION> <OPTION 

    value=http://www.forestry.gov.cn>林业局</OPTION> <OPTION 

    value=http://www.sipo.gov.cn>知识产权局</OPTION> <OPTION 

    value=http://www.cnta.gov.cn>旅游局</OPTION> <OPTION 

    value=http://www.sara.gov.cn/GB>宗教局</OPTION> <OPTION 

    value=http://www.counsellor.gov.cn>参事室</OPTION> <OPTION 

    value=http://www.ggj.gov.cn>国管局</OPTION> <OPTION 

    value=http://www.nbcp.gov.cn>预防腐败局</OPTION> <OPTION 

    value=http://www.gqb.gov.cn>侨办</OPTION> <OPTION 

    value=http://www.hmo.gov.cn>港澳办</OPTION> <OPTION 

    value=http://www.chinalaw.gov.cn>法制办</OPTION> <OPTION 

    value=http://www.xinhuanet.com>新华社</OPTION> <OPTION 

    value=http://www.cas.cn>中科院</OPTION> <OPTION 

    value=http://www.cass.net.cn>社科院</OPTION> <OPTION 

    value=http://www.cae.cn>工程院</OPTION> <OPTION 

    value=http://www.drc.gov.cn>发展研究中心</OPTION> <OPTION 

    value=http://www.nsa.gov.cn>行政学院</OPTION> <OPTION 

    value=http://www.cea.gov.cn>地震局</OPTION> <OPTION 

    value=http://www.cma.gov.cn>气象局</OPTION> <OPTION 

    value=http://www.cbrc.gov.cn>银监会</OPTION> <OPTION 

    value=http://www.csrc.gov.cn>证监会</OPTION> <OPTION 

    value=http://www.circ.gov.cn>保监会</OPTION> <OPTION 

    value=http://www.serc.gov.cn>电监会</OPTION> <OPTION 

    value=http://www.ssf.gov.cn>社保基金会</OPTION> <OPTION 

    value=http://www.nsfc.gov.cn>自然科学基金会</OPTION> <OPTION 

    value=http://www.gwytb.gov.cn>台办</OPTION> <OPTION 

    value=http://www.scio.gov.cn>新闻办</OPTION> <OPTION 

    value=http://www.saac.gov.cn>档案局</OPTION> <OPTION 

    value=http://www.gjxfj.gov.cn>信访局</OPTION> <OPTION 

    value=http://www.chinagrain.gov.cn>粮食局</OPTION> <OPTION 

    value=http://www.cic.gov.cn>国防科工局</OPTION> <OPTION 

    value=http://www.tobacco.gov.cn>烟草局</OPTION> <OPTION 

    value=http://www.safea.gov.cn>外专局</OPTION> <OPTION 

    value=http://www.soa.gov.cn>海洋局</OPTION> <OPTION 

    value=http://www.sbsm.gov.cn>测绘局</OPTION> <OPTION 

    value=http://www.caac.gov.cn>民航局</OPTION> <OPTION 

    value=http://www.caac.gov.cn>民航局</OPTION> <OPTION 

    value=http://www.post.gov.cn/index.htm>邮政局</OPTION> <OPTION 

    value=http://www.sach.gov.cn>文物局</OPTION> <OPTION 

    value=http://www.sda.gov.cn>食品药品监管局</OPTION> <OPTION 

    value=http://www.satcm.gov.cn>中医药局</OPTION> <OPTION 

    value=http://www.safe.gov.cn>外汇局</OPTION> <OPTION 

    value=http://www.chinasafety.gov.cn/mjweb/mjindex07.htm>煤矿安监局</OPTION> 

    <OPTION value=http://www.oscca.gov.cn>密码局</OPTION> <OPTION 

    value=http://www.cnsa.gov.cn>航天局</OPTION> <OPTION 

    value=http://www.caea.gov.cn>原子能机构</OPTION> <OPTION 

    value=http://www.china-language.gov.cn>国家语委</OPTION> <OPTION 

    value=http://www.cpad.gov.cn>国务院扶贫办</OPTION> <OPTION 

    value=http://www.3g.gov.cn>国务院三峡办</OPTION></SELECT> <SELECT name=select2 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>地方政府网站</OPTION> <OPTION 

    value=http://www.beijing.gov.cn>北京市</OPTION> <OPTION 

    value=http://www.shanghai.gov.cn>上海市</OPTION> <OPTION 

    value=http://www.tj.gov.cn>天津市</OPTION> <OPTION 

    value=http://www.cq.gov.cn>重庆市</OPTION> <OPTION 

    value=http://www.hlj.gov.cn>黑龙江</OPTION> <OPTION 

    value=http://www.jl.gov.cn>吉林省</OPTION> <OPTION 

    value=http://www.ln.gov.cn>辽宁省</OPTION> <OPTION 

    value=http://www.hebei.gov.cn>河北省</OPTION> <OPTION 

    value=http://www.shanxi.gov.cn>山西省</OPTION> <OPTION 

    value=http://www.jiangsu.gov.cn>江苏省</OPTION> <OPTION 

    value=http://www.zhejiang.gov.cn>浙江省</OPTION> <OPTION 

    value=http://www.ah.gov.cn>安徽省</OPTION> <OPTION 

    value=http://www.fujian.gov.cn>福建省</OPTION> <OPTION 

    value=http://www.jiangxi.gov.cn>江西省</OPTION> <OPTION 

    value=http://www.sd.gov.cn>山东省</OPTION> <OPTION 

    value=http://www.henan.gov.cn>河南省</OPTION> <OPTION 

    value=http://www.hubei.gov.cn>湖北省</OPTION> <OPTION 

    value=http://www.hunan.gov.cn>湖南省</OPTION> <OPTION 

    value=http://www.gd.gov.cn>广东省</OPTION> <OPTION 

    value=http://www.hainan.gov.cn>海南省</OPTION> <OPTION 

    value=http://www.sichuan.gov.cn>四川省</OPTION> <OPTION 

    value=http://www.gzgov.gov.cn>贵州省</OPTION> <OPTION 

    value=http://www.yn.gov.cn>云南省</OPTION> <OPTION 

    value=http://www.shaanxi.gov.cn>陕西省</OPTION> <OPTION 

    value=http://www.gansu.gov.cn>甘肃省</OPTION> <OPTION 

    value=http://www.qh.gov.cn>青海省</OPTION> <OPTION 

    value=http://www.nmg.gov.cn>内蒙古</OPTION> <OPTION 

    value=http://www.nx.cninfo.net>宁 夏</OPTION> <OPTION 

    value=http://www.xinjiang.gov.cn>新 疆</OPTION> <OPTION 

    value=http://www.gxi.gov.cn>广 西</OPTION> <OPTION 

    value=http://www.gxi.gov.cn>广 西</OPTION> <OPTION 

    value=http://www.tibetinfor.com>西 藏</OPTION> <OPTION 

    value=http://www.info.gov.hk>香 港</OPTION> <OPTION 

    value=http://www.macau.gov.mo>澳 门</OPTION></SELECT> <SELECT name=select3 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>驻港澳机构网站</OPTION> <OPTION 

    value=http://www.locpg.gov.cn>中央人民政府驻香港特别行政区联络办公室</OPTION></SELECT> <SELECT 

  name=select4 style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>世界海关组织</OPTION> <OPTION 

    value=http://www.wcoomd.org>世界海关组织</OPTION></SELECT> <SELECT name=select5 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>在京直属事业单位</OPTION> <OPTION 

    value=http://www.haiguanbook.com>海关出版社</OPTION> <OPTION 

    value=http://www.chinaport.gov.cn>电子口岸数据中心</OPTION></SELECT> <SELECT 

  name=select6 style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>社会团体</OPTION> <OPTION 

    value=http://www.caop.org.cn>口岸协会</OPTION> <OPTION 

    value=http://www.chinacba.org>报关协会</OPTION> <OPTION 

    value=http://www.cfea.org.cn>保税区出口加工区协会</OPTION></SELECT> <SELECT name=select6 

  style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>资讯网</OPTION> <OPTION 

    value=http://www.chinacustomsstat.com/customsstat>海关统计资讯网</OPTION> <OPTION 

    value=http://www.haiguan.info>海关信息网</OPTION> <OPTION 

    value=http://www.e-to-china.com.cn>中国通关网</OPTION> <OPTION 

    value=http://hgcg.customs.gov.cn>中国海关政府采购网</OPTION></SELECT> <SELECT 

  name=select8 style="FONT-SIZE: 12px; HEIGHT: 22px; WIDTH: 130px" 

  onchange=javascript:window.open(this.options[this.selectedIndex].value);this.selectedIndex=0 

  size=1> <OPTION selected>媒体</OPTION> <OPTION 

    value=http://www.people.com.cn/>人民网</OPTION></SELECT> </DIV></LI>

  <LI><A href="http://www.customs.gov.cn/publish/portal0/tab9406/#">举报电话</A> 

  <DIV class=showDiv style="DISPLAY: none">海关总署电话：走私举报：010-65222882 

  &nbsp;&nbsp;廉政举报：010-65124083 <BR>直属海关电话：<A 

  href="http://www.customs.gov.cn/tabid/3795/Default.aspx">热线电话</A> <A 

  href="http://www.customs.gov.cn/tabid/3853/Default.aspx">走私举报</A> <A 

  href="http://www.customs.gov.cn/tabid/3854/Default.aspx">廉政举报</A> 

</DIV></LI></UL></DIV>

<STYLE type=text/css>.showDiv1 {

	BORDER-TOP: #cbcbcb 1px solid; HEIGHT: 95px; BORDER-RIGHT: #cbcbcb 1px solid; WIDTH: 406px; BORDER-BOTTOM: #cbcbcb 1px solid; POSITION: absolute; LEFT: 0px; BORDER-LEFT: #cbcbcb 1px solid; Z-INDEX: 1000; TOP: -90px; BACKGROUND-COLOR: #fff

}

.showDiv1 P {

	COLOR: #555; PADDING-BOTTOM: 0px; TEXT-ALIGN: center; PADDING-TOP: 0px; PADDING-LEFT: 0px; LINE-HEIGHT: 20px; PADDING-RIGHT: 0px; magin: 0

}

</STYLE>



<SCRIPT type=text/javascript>

    jQuery(function () {

        jQuery("#yqFoot ul li:has(div)").hover(function () {

            jQuery(this).children("div").stop(true, true).fadeIn("slow");

        }, function () {

            jQuery(this).children("div").stop(true, true).fadeOut("slow");

        });

    })



</SCRIPT>

</DIV>

<SCRIPT type=text/javascript>

//<![CDATA[

    Sys.Application.initialize();

//]]>

</SCRIPT>

    </form>

</body>

</html>

