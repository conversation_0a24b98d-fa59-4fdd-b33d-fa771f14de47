Title: 🛰️ 获取网页信息
URL: https://www.drissionpage.cn/browser_control/get_page_info/

🛰️ 获取网页信息
云服务器38元/年起，大模型限免超7000万 tokens广告


成功访问网页后，可使用 Tab 对象属性和方法获取页面信息。
✅️️ 页面信息​
📌 html​
此属性返回当前页面 html 文本。
注意
html 文本不包含<iframe>元素内容。
返回类型：str
📌 json​
此属性把请求内容解析成 json。
假如用浏览器访问会返回 *.json 文件的 url，浏览器会把 json 数据显示出来，这个参数可以把这些数据转换为dict格式。
返回类型：dict
📌 title​
此属性返回当前页面title文本。
返回类型：str
📌 user_agent​
此属性返回当前页面 user agent 信息。
返回类型：str
📌 save()​
把当前页面保存为文件，同时返回保存的内容。
如果path和name参数都为None，只返回内容，不保存文件。
Page 对象和 Tab 对象有这个方法。
参数名称	类型	默认值	说明
path	str
Path	None	保存路径，为None且name不为None时保存到当前路径
name	str	None	保存的文件名，为None且path不为None时使用 title 值
as_pdf	bool	False	为Ture保存为 pdf，否则保存为 mhtml 且忽略kwargs参数
**kwargs	多种	无	pdf 生成参数
pdf 生成参数包括：landscape, displayHeaderFooter, printBackground, scale, paperWidth, paperHeight, marginTop, marginBottom, marginLeft, marginRight, pageRanges, headerTemplate, footerTemplate, preferCSSPageSize, generateTaggedPDF, generateDocumentOutline
返回类型	说明
str	as_pdf为False时返回 mhtml 文本
bytes	as_pdf为True时返回文件字节数据
✅️️ 运行状态信息​
📌 url​
此属性返回当前访问的 url。
返回类型：str
📌 tab_id​
返回类型：str
此属性返回当前标签页的 id。
📌 states.is_loading​
此属性返回页面是否正在加载状态。
返回类型：bool
📌 states.is_alive​
此属性返回页面是否仍然可用，标签页已关闭则返回False。
返回类型：bool
📌 states.ready_state​
此属性返回页面当前加载状态，有 4 种：
'connecting'： 网页连接中
'loading'：表示文档还在加载中
'interactive'：DOM 已加载，但资源未加载完成
'complete'：所有内容已完成加载
返回类型：str
📌 url_available​
此属性以布尔值返回当前链接是否可用。
返回类型：bool
📌 states.has_alert​
此属性以布尔值返回页面是否存在弹出框。
返回类型：bool
✅️️ 窗口信息​
📌 rect.size​
以tuple返回页面大小，格式：(宽, 高)。
返回类型：Tuple[int, int]
📌 rect.window_size​
此属性以tuple返回窗口大小，格式：(宽, 高)。
返回类型：Tuple[int, int]
📌 rect.window_location​
此属性以tuple返回窗口在屏幕上的坐标，左上角为(0, 0)。
返回类型：Tuple[int, int]
📌 rect.window_state​
此属性以返回窗口当前状态，有'normal'、'fullscreen'、'maximized'、 'minimized'几种。
返回类型：str
📌 rect.viewport_size​
此属性以tuple返回视口大小，不含滚动条，格式：(宽, 高)。
返回类型：Tuple[int, int]
📌 rect.viewport_size_with_scrollbar​
此属性以tuple返回浏览器窗口大小，含滚动条，格式：(宽, 高)。
返回类型：Tuple[int, int]
📌 rect.page_location​
此属性以tuple返回页面左上角在屏幕中坐标，左上角为(0, 0)。
返回类型：Tuple[int, int]
📌 rect.viewport_location​
此属性以tuple返回视口在屏幕中坐标，左上角为(0, 0)。
返回类型：Tuple[int, int]
📌 rect.scroll_position​
此属性返回页面滚动条位置，格式：(x, y)。
类型：Tuple[float, float]
✅️️ 配置参数信息​
📌 timeout​
此属性为整体默认超时时间（秒），包括元素查找、点击、处理提示框、列表选择等需要用到超时设置的地方，都以这个数据为默认值。默认为10。
返回类型：int、float
📌 timeouts​
此属性以字典方式返回三种超时时间（秒）。
'base'：与timeout属性是同一个值
'page_load'：用于等待页面加载
'script'：用于等待脚本执行
返回类型：dict
print(tab.timeouts)

输出：
{'base': 10, 'page_load': 30.0, 'script': 30.0}

📌 retry_times​
此属性为网络连接失败时的重试次数，默认为3。
返回类型：int
📌 retry_interval​
此属性为网络连接失败时的重试等待间隔秒数，默认为2。
返回类型：int、float
📌 load_mode​
此属性返回页面加载策略，有 3 种：
'normal'：等待页面所有资源完成加载
'eager'：DOM 加载完成即停止
'none'：页面完成连接即停止
返回类型：str
✅️️ cookies 和缓存信息​
📌 cookies()​
此方法以列表方式返回 cookies 信息。
参数名称	类型	默认值	说明
all_domains	bool	False	是否返回所有 cookies，为False只返回当前 url 的
all_info	bool	False	返回的 cookies 是否包含所有信息，False时只包含name、value、domain信息
返回类型	说明
CookiesList	cookies 组成的列表
示例：
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.get('https://www.baidu.com')

for i in tab.cookies():
    print(i)

输出：
{'domain': '.baidu.com', 'domain_specified': True, ......}
......

📌 指定返回类型​
cookies()方法返回的列表可转换为其它指定格式。
cookies().as_str()：'name1=value1; name2=value2'格式的字符串
cookies().as_dict()：{name1: value1, name2: value2}格式的字典
cookies().as_json()：json 格式的字符串
说明
as_str()和as_dict()都只会保留'name'和'value'字段。
📌 session_storage()​
此方法用于获取 sessionStorage 信息，可获取全部或单个项。
参数名称	类型	默认值	说明
item	str	None	要获取的项目，为None则返回全部项目组成的字典
返回类型	说明
dict	item参数为None时返回所有项目
str	指定item时返回该项目内容
📌 local_storage()​
此方法用于获取 localStorage 信息，可获取全部或单个项。
参数名称	类型	默认值	说明
item	str	None	要获取的项目，为None则返回全部项目组成的字典
返回类型	说明
dict	item参数为None时返回所有项目
str	指定item时返回该项目内容