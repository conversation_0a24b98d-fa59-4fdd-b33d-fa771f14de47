Title: 🛰️ 获取控制台信息
URL: https://www.drissionpage.cn/browser_control/console/

🛰️ 获取控制台信息


获取控制台信息的逻辑和监听网络数据差不多，是通过监听控制台数据实现的。
注意
不是所有显示在控制台的信息都能获取，需要用console.log()等方法输出到控制台的才能获取。
✅️ 示例​
from DrissionPage import Chromium

tab = Chromium().latest_tab
tab.console.start()
tab.run_js('console.log("DrissionPage");')
data = tab.console.wait()
print(data.text)  # 输出：DrissionPage

✅️ 启动和停止​
📌 console.start()​
此方法用于启动控制台信息监听。
参数： 无
返回：None
📌 console.stop()​
此方法用于停止监听，清空已监听到的信息列表。
参数： 无
返回：None
✅️ 获取信息​
📌 console.wait()​
此方法用于等待一条控制台信息。
参数名称	类型	默认值	说明
timeout	float
None	None	超时时间（秒），为None无限等待
返回类型	说明
ConsoleData	控制台信息数据包对象
False	等待超时时
📌 console.steps()​
此方法返回一个可迭代对象，用于for循环，每次循环可从中获取到的信息。
可实现实时获取并返回数据包。
如果timeout超时，会中断循环。
参数名称	类型	默认值	说明
timeout	float
None	None	每个信息等待时间（秒），为None表示无限等待
返回类型	说明
ConsoleData	控制台信息数据包对象
📌 console.messages​
此属性以list方式返回获取到的信息，返回后会清空列表。
返回类型	说明
List[ConsoleData]	控制台信息对象组成的列表
✅️ 其它​
📌 console.listening​
此属性返回监听是否进行中。
返回：bool
📌 console.clear()​
此方法用于清空已获取但未返回的信息。
参数： 无
返回：None
✅️ ConsoleData对象​
ConsoleData对象是获取到的数据包结果对象，包含了数据包各种信息。
📌 对象属性​
属性名称	数据类型	说明
source	str	来源
level	str	类型
text	str	内容文本
body	Any	把text进行 json 解析
url	str	网址
line	str	行号
column	str	列号