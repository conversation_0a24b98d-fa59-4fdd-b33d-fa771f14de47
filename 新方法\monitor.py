# -*- coding: utf-8 -*-
"""
实时监控查询进度和性能
"""

import os
import json
import time
import threading
from datetime import datetime

class QueryMonitor:
    def __init__(self, result_file_prefix):
        self.result_file_prefix = result_file_prefix
        self.json_file = f"{result_file_prefix}.json"
        self.last_count = 0
        self.start_time = time.time()
        self.monitoring = False
        
    def start_monitoring(self, interval=10):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        
        print("=== 查询监控启动 ===")
        print(f"监控文件: {self.json_file}")
        print(f"更新间隔: {interval} 秒")
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("按 Ctrl+C 停止监控\n")
        
        try:
            while self.monitoring:
                self.check_progress()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n监控已停止")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def check_progress(self):
        """检查查询进度"""
        try:
            if not os.path.exists(self.json_file):
                print(f"等待结果文件生成... {datetime.now().strftime('%H:%M:%S')}")
                return
            
            # 读取当前结果
            with open(self.json_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            current_count = len(results)
            
            if current_count == self.last_count:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 等待新结果... (当前: {current_count})")
                return
            
            # 计算统计信息
            successful = len([r for r in results if r.get('status') == 'success'])
            failed = len([r for r in results if r.get('status') != 'success'])
            
            # 计算速度
            elapsed = time.time() - self.start_time
            total_rate = current_count / elapsed if elapsed > 0 else 0
            
            # 计算增量速度
            if hasattr(self, 'last_time'):
                time_diff = time.time() - self.last_time
                count_diff = current_count - self.last_count
                current_rate = count_diff / time_diff if time_diff > 0 else 0
            else:
                current_rate = total_rate
            
            # 打印进度
            print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                  f"总数: {current_count} | "
                  f"成功: {successful} | "
                  f"失败: {failed} | "
                  f"成功率: {(successful/current_count*100):.1f}% | "
                  f"总速度: {total_rate:.2f}/s | "
                  f"当前速度: {current_rate:.2f}/s")
            
            # 更新记录
            self.last_count = current_count
            self.last_time = time.time()
            
        except Exception as e:
            print(f"监控错误: {e}")
    
    def show_final_report(self):
        """显示最终报告"""
        if not os.path.exists(self.json_file):
            print("没有找到结果文件")
            return
        
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            total = len(results)
            successful = len([r for r in results if r.get('status') == 'success'])
            failed = total - successful
            
            elapsed = time.time() - self.start_time
            average_rate = total / elapsed if elapsed > 0 else 0
            
            print(f"\n{'='*50}")
            print("最终统计报告")
            print(f"{'='*50}")
            print(f"总查询数: {total}")
            print(f"成功查询: {successful}")
            print(f"失败查询: {failed}")
            print(f"成功率: {(successful/total*100):.2f}%")
            print(f"总耗时: {elapsed:.2f} 秒")
            print(f"平均速度: {average_rate:.2f} 个/秒")
            
            # 错误分析
            error_types = {}
            for result in results:
                if result.get('status') != 'success':
                    status = result.get('status', 'unknown')
                    error_types[status] = error_types.get(status, 0) + 1
            
            if error_types:
                print(f"\n错误类型分布:")
                for error_type, count in error_types.items():
                    print(f"  {error_type}: {count}")
                    
        except Exception as e:
            print(f"生成报告时出错: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python monitor.py <结果文件前缀>")
        print("例如: python monitor.py test_results")
        return
    
    result_file_prefix = sys.argv[1]
    
    # 监控间隔
    interval = 10
    if len(sys.argv) > 2:
        try:
            interval = int(sys.argv[2])
        except:
            interval = 10
    
    monitor = QueryMonitor(result_file_prefix)
    
    try:
        monitor.start_monitoring(interval)
    finally:
        monitor.show_final_report()

if __name__ == "__main__":
    main()