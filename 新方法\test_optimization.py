# -*- coding: utf-8 -*-
"""
测试优化后的查询系统性能
"""

import time
import random
from api_cracker import CustomsAPICracker

def test_performance():
    """测试性能提升"""
    print("=== 海关查询系统性能测试 ===")
    
    # 生成测试批次号
    test_manifest_ids = [f"TEST{random.randint(100000, 999999)}" for _ in range(100)]
    
    print(f"测试批次号数量: {len(test_manifest_ids)}")
    
    # 测试不同并发数的性能
    for workers in [5, 10, 15, 20]:
        print(f"\n--- 测试 {workers} 个并发 ---")
        
        cracker = CustomsAPICracker(max_workers=workers)
        start_time = time.time()
        
        # 运行一小批测试
        test_batch = test_manifest_ids[:20]  # 只测试20个
        results = cracker.batch_query(
            manifest_ids=test_batch,
            query_type='road_transport',
            output_file=f'test_results_{workers}_workers'
        )
        
        elapsed = time.time() - start_time
        rate = len(test_batch) / elapsed
        
        print(f"并发数: {workers}")
        print(f"耗时: {elapsed:.2f} 秒")
        print(f"速度: {rate:.2f} 个/秒")
        print(f"成功: {cracker.stats['successful_queries']}")
        print(f"失败: {cracker.stats['failed_queries']}")
        print(f"重试: {cracker.stats['retry_queries']}")

def test_retry_mechanism():
    """测试重试机制"""
    print("\n=== 测试重试机制 ===")
    
    cracker = CustomsAPICracker(max_workers=5)
    
    # 使用一个肯定会出错的批次号来测试重试
    test_id = "INVALID_TEST_123"
    
    print(f"测试批次号: {test_id}")
    print("预期会触发重试机制...")
    
    start_time = time.time()
    result = cracker.query_single_with_retry(test_id, 'road_transport')
    elapsed = time.time() - start_time
    
    print(f"结果状态: {result['status']}")
    print(f"耗时: {elapsed:.2f} 秒")
    print(f"重试次数: {cracker.stats['retry_queries']}")

def test_batch_save():
    """测试分批保存功能"""
    print("\n=== 测试分批保存功能 ===")
    
    # 生成测试数据
    test_results = []
    for i in range(75):  # 超过batch_size(50)
        test_results.append({
            'manifest_id': f'TEST{i:06d}',
            'status': 'success' if i % 3 == 0 else 'no_record',
            'data': [['测试数据', f'值{i}', '示例']] if i % 3 == 0 else None
        })
    
    cracker = CustomsAPICracker(max_workers=1)
    
    print(f"模拟保存 {len(test_results)} 个结果")
    print(f"分批大小: {cracker.batch_size}")
    
    # 分批保存
    for i in range(0, len(test_results), cracker.batch_size):
        batch = test_results[i:i+cracker.batch_size]
        cracker.save_batch_results(batch, 'test_batch_save')
        print(f"已保存批次 {i//cracker.batch_size + 1}: {len(batch)} 个结果")

if __name__ == "__main__":
    print("开始性能测试...")
    print("注意: 这是模拟测试，不会进行真实的网络请求")
    
    # 只测试重试机制和分批保存，不进行真实网络请求
    test_retry_mechanism()
    test_batch_save()
    
    print("\n测试完成!")
    print("\n要进行真实性能测试，请运行:")
    print("python high_performance_query.py 您的数据文件.csv dual --workers 20")