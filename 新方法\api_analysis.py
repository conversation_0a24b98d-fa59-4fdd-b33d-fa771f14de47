# -*- coding: utf-8 -*-
"""
海关查询系统API分析脚本
使用DrissionPage监听网络请求，分析API结构和验证码机制
"""

from DrissionPage import Chromium, ChromiumOptions
import json
import time
import requests
from urllib.parse import parse_qs, urlparse
import base64

class CustomsAPIAnalyzer:
    def __init__(self):
        # 初始化浏览器
        co = ChromiumOptions().auto_port()
        self.browser = Chromium(addr_or_opts=co)
        self.tab = self.browser.latest_tab
        
        # 存储分析结果
        self.api_requests = []
        self.captcha_requests = []
        self.form_data = {}
        
    def analyze_road_transport_query(self):
        """分析公路舱单查询页面"""
        print("=== 分析公路舱单查询页面 ===")
        
        # 开始监听所有网络请求
        self.tab.listen.start(targets=True, method=True)
        
        # 访问页面
        url = 'http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx'
        print(f"访问页面: {url}")
        self.tab.get(url)
        
        # 等待页面加载完成
        self.tab.wait.doc_loaded(10)
        time.sleep(2)
        
        # 获取页面加载时的网络请求
        packets = self.tab.listen.wait(count=10, timeout=5, fit_count=False)
        if packets:
            print(f"捕获到 {len(packets)} 个网络请求")
            for packet in packets:
                self.analyze_packet(packet, "页面加载")
        
        # 分析验证码请求
        self.analyze_captcha_mechanism()
        
        # 分析表单提交
        self.analyze_form_submission()
        
    def analyze_road_query(self):
        """分析提运单信息查询页面"""
        print("\n=== 分析提运单信息查询页面 ===")
        
        # 清空之前的监听
        self.tab.listen.stop()
        self.tab.listen.start(targets=True, method=True)
        
        # 访问页面
        url = 'http://query.customs.gov.cn/MNFTQ/MRoadQuery.aspx'
        print(f"访问页面: {url}")
        self.tab.get(url)
        
        # 等待页面加载完成
        self.tab.wait.doc_loaded(10)
        time.sleep(2)
        
        # 获取页面加载时的网络请求
        packets = self.tab.listen.wait(count=10, timeout=5, fit_count=False)
        if packets:
            print(f"捕获到 {len(packets)} 个网络请求")
            for packet in packets:
                self.analyze_packet(packet, "页面加载")
        
        # 分析验证码请求
        self.analyze_captcha_mechanism()
        
        # 分析表单提交
        self.analyze_form_submission()
    
    def analyze_captcha_mechanism(self):
        """分析验证码机制"""
        print("\n--- 分析验证码机制 ---")
        
        # 查找验证码图片元素
        captcha_elements = [
            '@id=MRoadTransportQueryCtrl1_Image1',  # 公路舱单查询验证码
            '@id=MRoadQueryCtrl1_imgCode'           # 提运单信息查询验证码
        ]
        
        for selector in captcha_elements:
            element = self.tab.ele(selector, timeout=2)
            if element:
                print(f"找到验证码元素: {selector}")
                
                # 获取验证码图片的src属性
                src = element.attr('src')
                print(f"验证码图片URL: {src}")
                
                # 分析验证码请求
                if src:
                    self.analyze_captcha_url(src)
                
                # 尝试刷新验证码
                try:
                    element.click()
                    time.sleep(1)
                    
                    # 监听刷新验证码的请求
                    packets = self.tab.listen.wait(count=5, timeout=3, fit_count=False)
                    if packets:
                        for packet in packets:
                            if 'image' in packet.url.lower() or 'captcha' in packet.url.lower():
                                self.analyze_packet(packet, "验证码刷新")
                except:
                    print("无法点击验证码刷新")
                
                break
    
    def analyze_captcha_url(self, src):
        """分析验证码URL结构"""
        print(f"分析验证码URL: {src}")
        
        if src.startswith('data:image'):
            print("验证码是base64编码的内联图片")
            # 解析base64数据
            try:
                header, data = src.split(',', 1)
                image_data = base64.b64decode(data)
                print(f"验证码图片大小: {len(image_data)} bytes")
            except:
                print("无法解析base64验证码")
        else:
            # 分析URL参数
            parsed = urlparse(src)
            params = parse_qs(parsed.query)
            print(f"验证码URL路径: {parsed.path}")
            print(f"验证码URL参数: {params}")
    
    def analyze_form_submission(self):
        """分析表单提交过程"""
        print("\n--- 分析表单提交过程 ---")
        
        # 查找输入框和按钮
        input_selectors = [
            ('@id=MRoadTransportQueryCtrl1_txtManifestID', '@id=MRoadTransportQueryCtrl1_txtCode', '@id=MRoadTransportQueryCtrl1_btQuery'),
            ('@id=MRoadQueryCtrl1_txtManifestID', '@id=MRoadQueryCtrl1_txtCode', '@id=MRoadQueryCtrl1_btQuery')
        ]
        
        for manifest_input, code_input, submit_btn in input_selectors:
            manifest_elem = self.tab.ele(manifest_input, timeout=2)
            code_elem = self.tab.ele(code_input, timeout=2)
            submit_elem = self.tab.ele(submit_btn, timeout=2)
            
            if manifest_elem and code_elem and submit_elem:
                print(f"找到表单元素: {manifest_input}")
                
                # 填入测试数据
                test_manifest = "TEST123456"
                test_code = "1234"
                
                manifest_elem.clear()
                manifest_elem.input(test_manifest)
                code_elem.clear()
                code_elem.input(test_code)
                
                # 监听提交请求
                self.tab.listen.start(targets=True, method=['POST', 'GET'])
                
                # 点击提交
                submit_elem.click()
                
                # 等待响应
                time.sleep(3)
                
                # 获取提交请求
                packets = self.tab.listen.wait(count=5, timeout=5, fit_count=False)
                if packets:
                    for packet in packets:
                        if packet.method in ['POST', 'GET']:
                            self.analyze_packet(packet, "表单提交")
                
                break
    
    def analyze_packet(self, packet, request_type):
        """分析单个网络请求包"""
        print(f"\n[{request_type}] {packet.method} {packet.url}")
        
        # 分析请求
        if packet.request:
            print(f"请求头: {dict(packet.request.headers)}")
            if packet.request.postData:
                print(f"POST数据: {packet.request.postData}")
        
        # 分析响应
        if packet.response:
            print(f"响应状态: {packet.response.status}")
            print(f"响应头: {dict(packet.response.headers)}")
            
            # 如果是JSON响应，尝试解析
            if packet.response.body and isinstance(packet.response.body, dict):
                print(f"JSON响应: {json.dumps(packet.response.body, ensure_ascii=False, indent=2)}")
            elif packet.response.body and len(str(packet.response.body)) < 1000:
                print(f"响应内容: {packet.response.body}")
        
        # 存储重要请求
        request_info = {
            'type': request_type,
            'method': packet.method,
            'url': packet.url,
            'headers': dict(packet.request.headers) if packet.request else {},
            'post_data': packet.request.postData if packet.request else None,
            'response_status': packet.response.status if packet.response else None,
            'response_body': packet.response.body if packet.response else None
        }
        
        if 'image' in packet.url.lower() or 'captcha' in packet.url.lower():
            self.captcha_requests.append(request_info)
        else:
            self.api_requests.append(request_info)
    
    def save_analysis_results(self):
        """保存分析结果"""
        # 处理不能JSON序列化的数据
        def clean_data(obj):
            if isinstance(obj, bytes):
                try:
                    return obj.decode('utf-8')
                except:
                    return f"<bytes data: {len(obj)} bytes>"
            elif isinstance(obj, dict):
                return {k: clean_data(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [clean_data(item) for item in obj]
            else:
                return obj

        results = {
            'api_requests': clean_data(self.api_requests),
            'captcha_requests': clean_data(self.captcha_requests),
            'form_data': clean_data(self.form_data),
            'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        with open('api_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n分析结果已保存到 api_analysis_results.json")
        print(f"共捕获 {len(self.api_requests)} 个API请求")
        print(f"共捕获 {len(self.captcha_requests)} 个验证码请求")
    
    def close(self):
        """关闭浏览器"""
        self.browser.quit()

def main():
    analyzer = CustomsAPIAnalyzer()
    
    try:
        # 分析两个查询页面
        analyzer.analyze_road_transport_query()
        analyzer.analyze_road_query()
        
        # 保存结果
        analyzer.save_analysis_results()
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
