# 海关数据查询系统使用说明

## 系统概述

本系统是一个自动化海关数据查询工具，主要用于批量查询和提取海关数据，包括公路舱单、承运确保和提运单信息。系统采用分布式并行处理的方式提高查询效率，并将查询结果最终导入到Oracle数据库中。

## 系统架构

系统由以下几个主要部分组成：

1. **数据分割模块**：将大量查询数据分割成小批次，便于并行处理
2. **自动查询模块**：使用网页自动化技术进行海关数据查询
3. **数据合并模块**：将多个查询结果合并为单一文件
4. **数据导入模块**：将合并后的数据导入到Oracle数据库

## 工作流程

整个系统的工作流程如下：

1. **准备阶段**：准备包含查询批次号的CSV文件
2. **数据分割**：使用分割脚本将CSV文件分割成多个小文件，并复制查询脚本到各个子目录
3. **并行查询**：在多个终端中同时运行查询脚本，对分割后的数据进行查询
4. **结果合并**：使用合并脚本将各个查询结果合并为单一的TXT文件
5. **数据导入**：使用数据库导入脚本将合并后的数据导入到Oracle数据库

## 主要脚本说明

### 1. 数据分割脚本（分割和复制的.ipynb）

**功能**：将大型CSV文件分割成多个小文件，并复制查询脚本到各个子目录

**使用方法**：
- 打开`base承运确保/分割和复制的.ipynb`或`base提运单信息/分割和复制的.ipynb`
- 运行第一个单元格，将CSV文件分割成多个部分（默认每部分200条记录）
- 系统会自动创建`分割后的文件目录`，并在其中创建多个子目录（part_1, part_2, ...）
- 每个子目录中包含一个CSV文件和一个查询脚本

### 2. 自动查询脚本（demo.py / 提运单信息.py）

**功能**：自动访问海关查询网站，输入批次号，识别验证码，提取查询结果

**使用方法**：
- 在每个分割后的子目录（如`part_1`, `part_2`等）中运行查询脚本
- 对于承运确保信息：`python demo.py`
- 对于提运单信息：`python 提运单信息.py`
- 查询结果会保存在各自的`数据落地`目录中

**技术特点**：
- 使用`DrissionPage`进行网页自动化操作
- 使用`ddddocr`进行验证码识别
- 自动处理验证码错误和重试机制

### 3. 数据合并脚本（分割和复制的.ipynb）

**功能**：将多个查询结果文件合并为一个完整的TXT文件

**使用方法**：
- 在所有查询完成后，打开`分割和复制的.ipynb`
- 运行第二个单元格，将`数据落地`目录中的所有TXT文件合并
- 合并后的文件将保存为：
  - 承运确保：`base承运确保/承运确报信息all.txt`
  - 提运单信息：`base提运单信息/base提运单信息.txt`

### 4. 数据库导入脚本（数据库导入.py）

**功能**：将合并后的TXT文件数据导入到Oracle数据库

**使用方法**：
- 运行`python 数据库导入.py`
- 脚本会自动连接数据库，并将数据导入到相应的表中
- 默认情况下不会清空表数据（truncate_tables函数已被注释）

**数据库表结构**：
- `temp_cyqb`（承运确保）：包含SELECTION, BATCH_NUMBER, VEHICLE_NUMBER等字段
- `temp_tydxx`（提运单信息）：包含BATCH_NUMBER, BILL_NUMBER, IMPORT_EXPORT等字段

## 注意事项

1. **环境依赖**：
   - Python 3.x
   - 必要的Python库：DrissionPage, pandas, ddddocr, cx_Oracle等
   - Oracle客户端

2. **并行处理**：
   - 为了提高效率，建议同时运行多个查询脚本
   - 可以打开多个命令行窗口，分别进入不同的子目录运行脚本

3. **数据库连接**：
   - 数据库连接信息：manifest_dcb/manifest_dcb@192.168.1.151/TEST
   - 如需修改连接信息，请编辑`数据库导入.py`文件中的相关变量

4. **错误处理**：
   - 查询脚本包含自动重试机制，遇到验证码错误会自动重试
   - 数据库导入脚本包含异常处理，导入失败会回滚事务

## 查询网站

系统查询的网站包括：

- 公路舱单查询：http://query.customs.gov.cn/MNFTQ/MRoadQuery.aspx
- 承运确保查询：http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx

## 文件结构

```
公路仓单查询的/
├── 数据库导入.py                  # 数据库导入脚本
├── base承运确保/                  # 承运确保相关文件
│   ├── demo.py                   # 承运确保查询脚本
│   ├── 分割和复制的.ipynb          # 数据分割和合并脚本
│   ├── 承运确保.ipynb             # 承运确保查询Notebook版本
│   ├── 跨境清单批次号.csv          # 原始查询数据
│   ├── 承运确报信息all.txt        # 合并后的查询结果
│   ├── 分割后的文件目录/           # 分割后的文件
│   │   ├── part_1/              # 第一部分数据
│   │   │   ├── demo.py         # 查询脚本副本
│   │   │   └── part_1.csv      # 分割后的数据
│   │   ├── part_2/              # 第二部分数据
│   │   └── ...
│   └── 数据落地/                  # 查询结果存放目录
├── base提运单信息/                 # 提运单信息相关文件
│   ├── 提运单信息.py              # 提运单信息查询脚本
│   ├── 分割和复制的.ipynb          # 数据分割和合并脚本
│   ├── 跨境清单批次号.csv          # 原始查询数据
│   ├── base提运单信息.txt         # 合并后的查询结果
│   ├── 分割后的文件目录/           # 分割后的文件
│   │   ├── part_1/              # 第一部分数据
│   │   │   ├── 提运单信息.py     # 查询脚本副本
│   │   │   └── part_1.csv      # 分割后的数据
│   │   ├── part_2/              # 第二部分数据
│   │   └── ...
│   └── 数据落地/                  # 查询结果存放目录
└── 公路舱单-提运单信息.ipynb        # 公路舱单查询Notebook
``` 