# 海关查询系统优化说明

## 主要优化内容

### 1. 性能优化
- **并发数提升**: 从5个线程增加到20个线程，提升4倍并发能力
- **连接池优化**: 增加HTTP连接池配置，减少连接建立开销
- **超时设置**: 优化网络超时参数，避免长时间等待

### 2. 重试机制
- **自动重试**: 对网络异常、连接断开等错误自动重试最多3次
- **智能重试**: 只对可恢复的错误进行重试，避免无意义重试
- **重试延迟**: 重试间隔1秒，避免过于频繁的请求

### 3. 数据及时落地
- **分批保存**: 每50个结果保存一次，避免数据丢失
- **增量保存**: 支持追加模式保存，不会覆盖已有数据
- **双格式保存**: 同时保存JSON和CSV格式

### 4. 错误处理优化
- **减少噪音**: 常见网络错误不打印日志，减少输出干扰
- **详细统计**: 增加重试次数统计
- **错误分类**: 区分不同类型的错误

## 性能预期

### 之前的性能
- 并发数: 5
- 速度: 约2.2个/秒
- 重试: 无
- 数据落地: 仅在最后

### 优化后性能
- 并发数: 20
- 预期速度: 8-10个/秒（提升4-5倍）
- 重试: 自动重试3次
- 数据落地: 每50个结果保存一次

## 使用方法

### 基本使用
```bash
# 使用默认20个并发
python high_performance_query.py 输入文件.csv dual

# 自定义并发数（如果服务器负载高可以适当降低）
python high_performance_query.py 输入文件.csv dual --workers 15

# 指定输出目录
python high_performance_query.py 输入文件.csv dual --output 结果目录
```

### 性能调优建议
1. **并发数调整**: 根据网络情况调整，建议15-25之间
2. **网络监控**: 注意观察网络延迟和丢包率
3. **服务器负载**: 避免在服务器高峰期运行

## 错误处理

### 常见错误类型
- `Response ended prematurely`: 网络连接断开，会自动重试
- `timeout`: 网络超时，会自动重试
- `captcha_error`: 验证码获取失败，会自动重试
- `max_retries_exceeded`: 超过最大重试次数

### 监控指标
- 成功率: 应保持在95%以上
- 重试率: 正常情况下应低于10%
- 速度: 应在8-10个/秒范围内

## 数据保护

### 自动保存机制
- 每50个结果自动保存到文件
- 程序异常退出时已处理的数据不会丢失
- 支持断点续传（手动重启时从失败的地方继续）

### 文件格式
- `results.json`: 完整的查询结果（包含错误信息）
- `results.csv`: 仅成功的数据（用于数据库导入）
- `query_report.json`: 详细的统计报告