# -*- coding: utf-8 -*-
"""
调试查询脚本 - 分析失败原因并测试两个查询系统
"""

import pandas as pd
from api_cracker import CustomsAPICracker
import time

def test_single_queries():
    """测试单个查询，分析失败原因"""
    print("=== 测试单个查询 ===")
    
    # 读取一些真实的批次号
    df = pd.read_csv('202502190824.csv')
    test_ids = df.iloc[:5, 0].astype(str).tolist()  # 取前5个进行测试
    
    cracker = CustomsAPICracker(max_workers=1)
    
    print("测试公路舱单查询:")
    for i, manifest_id in enumerate(test_ids):
        print(f"\n测试 {i+1}: {manifest_id}")
        result = cracker.query_single(manifest_id, 'road_transport')
        print(f"结果: {result}")
        
        if result['status'] not in ['success', 'no_record']:
            print(f"失败原因: {result}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "="*50)
    print("测试提运单信息查询:")
    for i, manifest_id in enumerate(test_ids):
        print(f"\n测试 {i+1}: {manifest_id}")
        result = cracker.query_single(manifest_id, 'road_query')
        print(f"结果: {result}")
        
        if result['status'] not in ['success', 'no_record']:
            print(f"失败原因: {result}")
        
        time.sleep(1)

def test_captcha_mechanism():
    """测试验证码机制"""
    print("\n=== 测试验证码机制 ===")
    
    import requests
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    })
    
    # 测试两个系统的验证码
    captcha_urls = [
        'http://query.customs.gov.cn/MNFTQ/Image.aspx',  # 通用验证码URL
    ]
    
    for url in captcha_urls:
        print(f"\n测试验证码URL: {url}")
        try:
            response = session.get(url)
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('Content-Type')}")
            print(f"Cookies: {dict(response.cookies)}")
            
            # 检查Cookie中的验证码
            verify_code = response.cookies.get('Verify') or response.cookies.get('VerfyRoadCode')
            if verify_code:
                print(f"验证码答案: {verify_code}")
            else:
                print("未在Cookie中找到验证码答案")
                
        except Exception as e:
            print(f"请求失败: {e}")

def analyze_form_differences():
    """分析两个查询表单的差异"""
    print("\n=== 分析表单差异 ===")
    
    import requests
    import re
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    })
    
    pages = [
        {
            'name': '公路舱单查询',
            'url': 'http://query.customs.gov.cn/MNFTQ/MRoadTransportQuery.aspx'
        },
        {
            'name': '提运单信息查询', 
            'url': 'http://query.customs.gov.cn/MNFTQ/MRoadQuery.aspx'
        }
    ]
    
    for page in pages:
        print(f"\n分析页面: {page['name']}")
        try:
            response = session.get(page['url'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                html = response.text
                
                # 查找表单字段
                input_pattern = r'<input[^>]*name="([^"]*)"[^>]*>'
                inputs = re.findall(input_pattern, html, re.IGNORECASE)
                print(f"表单字段: {inputs}")
                
                # 查找验证码图片
                img_pattern = r'<img[^>]*id="([^"]*)"[^>]*src="([^"]*)"[^>]*>'
                images = re.findall(img_pattern, html, re.IGNORECASE)
                captcha_images = [img for img in images if 'image' in img[0].lower() or 'code' in img[0].lower()]
                print(f"验证码图片: {captcha_images}")
                
                # 查找提交按钮
                button_pattern = r'<input[^>]*type="submit"[^>]*name="([^"]*)"[^>]*value="([^"]*)"[^>]*>'
                buttons = re.findall(button_pattern, html, re.IGNORECASE)
                print(f"提交按钮: {buttons}")
                
        except Exception as e:
            print(f"分析失败: {e}")

def test_batch_performance():
    """测试批量查询性能"""
    print("\n=== 测试批量查询性能 ===")
    
    # 读取少量数据进行性能测试
    df = pd.read_csv('202502190824.csv')
    test_ids = df.iloc[:20, 0].astype(str).tolist()  # 取20个进行测试
    
    print(f"测试批次号数量: {len(test_ids)}")
    
    # 测试不同并发数的性能
    for workers in [1, 5, 10]:
        print(f"\n测试并发数: {workers}")
        
        cracker = CustomsAPICracker(max_workers=workers)
        start_time = time.time()
        
        results = cracker.batch_query(
            manifest_ids=test_ids,
            query_type='road_transport'
        )
        
        elapsed = time.time() - start_time
        rate = len(test_ids) / elapsed
        
        successful = len([r for r in results if r['status'] == 'success'])
        no_record = len([r for r in results if r['status'] == 'no_record'])
        errors = len([r for r in results if r['status'] not in ['success', 'no_record']])
        
        print(f"耗时: {elapsed:.2f}秒, 速度: {rate:.2f}个/秒")
        print(f"成功: {successful}, 无记录: {no_record}, 错误: {errors}")

def main():
    print("海关查询系统调试工具")
    print("="*50)
    
    # 1. 测试验证码机制
    test_captcha_mechanism()
    
    # 2. 分析表单差异
    analyze_form_differences()
    
    # 3. 测试单个查询
    test_single_queries()
    
    # 4. 测试批量性能
    test_batch_performance()

if __name__ == "__main__":
    main()
